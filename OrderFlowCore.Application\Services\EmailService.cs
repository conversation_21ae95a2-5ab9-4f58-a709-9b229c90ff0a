using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Application.Services;

public class EmailService : IEmailService
{
    private readonly EmailSettings _settings;

    public EmailService(IOptions<EmailSettings> settings)
    {
        _settings = settings.Value;
    }

    public async Task<bool> SendAsync(string toEmail, string subject, string body, bool isHtml = true)
    {
        if (string.IsNullOrWhiteSpace(_settings.Host) || string.IsNullOrWhiteSpace(_settings.FromAddress))
            return false;

        using var message = new MailMessage()
        {
            From = new MailAddress(_settings.FromAddress, _settings.FromName),
            Subject = subject,
            Body = body,
            IsBodyHtml = isHtml
        };
        message.To.Add(new MailAddress(toEmail));

        using var client = new SmtpClient(_settings.Host, _settings.Port)
        {
            EnableSsl = _settings.EnableSsl,
            UseDefaultCredentials = false,
            Credentials = !string.IsNullOrEmpty(_settings.Username)
                ? new NetworkCredential(_settings.Username, _settings.Password)
                : CredentialCache.DefaultNetworkCredentials
        };

        await client.SendMailAsync(message);
        return true;
    }
}

