using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Admin, UserRole.Manager)]
    public class QualificationController : Controller
    {
        private readonly IQualificationService _qualificationService;
        
        private readonly ILogger<QualificationController> _logger;

        public QualificationController(IQualificationService qualificationService, ILogger<QualificationController> logger)
        {
            _qualificationService = qualificationService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _qualificationService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new QualificationDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(QualificationDto qualification)
        {
            if (!ModelState.IsValid)
            {
                return View(qualification);
            }

            var result = await _qualificationService.CreateAsync(qualification);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(qualification);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _qualificationService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(QualificationDto qualification)
        {
            if (ModelState.IsValid)
            {
                var result = await _qualificationService.UpdateAsync(qualification);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(qualification);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _qualificationService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportToExcel()
        {
            try
            {
                var result = await _qualificationService.ExportToExcelAsync();
                if (result.IsSuccess)
                {
                    var fileName = $"المؤهلات_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting qualifications to Excel");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير المؤهلات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFromExcel(IFormFile excelFile)
        {
            try
            {
                if (excelFile == null || excelFile.Length == 0)
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel" });
                }

                if (!excelFile.FileName.EndsWith(".xlsx") && !excelFile.FileName.EndsWith(".xls"))
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)" });
                }

                using var stream = excelFile.OpenReadStream();
                var result = await _qualificationService.ImportFromExcelAsync(stream);

                if (result.IsSuccess)
                {
                    return Json(new {
                        success = true,
                        message = result.Message,
                        importedCount = result.Data,
                        errors = result.Errors
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing qualifications from Excel");
                return Json(new { success = false, message = "حدث خطأ أثناء استيراد المؤهلات" });
            }
        }
    }
}