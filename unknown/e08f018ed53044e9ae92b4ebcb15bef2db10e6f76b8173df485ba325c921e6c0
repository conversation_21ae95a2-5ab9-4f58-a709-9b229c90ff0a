using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Admin, UserRole.Manager)]
    public class EmploymentTypeController : Controller
    {
        private readonly IEmploymentTypeService _employmentTypeService;
        private readonly ILogger<EmploymentTypeController> _logger;

        public EmploymentTypeController(IEmploymentTypeService employmentTypeService, ILogger<EmploymentTypeController> logger)
        {
            _employmentTypeService = employmentTypeService;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _employmentTypeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new EmploymentTypeDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(EmploymentTypeDto employmentType)
        {
            if (!ModelState.IsValid)
            {
                return View(employmentType);
            }

            var result = await _employmentTypeService.CreateAsync(employmentType);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(employmentType);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _employmentTypeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(EmploymentTypeDto employmentType)
        {
            if (ModelState.IsValid)
            {
                var result = await _employmentTypeService.UpdateAsync(employmentType);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(employmentType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _employmentTypeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportToExcel()
        {
            try
            {
                var result = await _employmentTypeService.ExportToExcelAsync();
                if (result.IsSuccess)
                {
                    var fileName = $"أنواع_التوظيف_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting employment types to Excel");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير أنواع التوظيف";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFromExcel(IFormFile excelFile)
        {
            try
            {
                if (excelFile == null || excelFile.Length == 0)
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel" });
                }

                if (!excelFile.FileName.EndsWith(".xlsx") && !excelFile.FileName.EndsWith(".xls"))
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)" });
                }

                using var stream = excelFile.OpenReadStream();
                var result = await _employmentTypeService.ImportFromExcelAsync(stream);

                if (result.IsSuccess)
                {
                    return Json(new {
                        success = true,
                        message = result.Message,
                        importedCount = result.Data,
                        errors = result.Errors
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing employment types from Excel");
                return Json(new { success = false, message = "حدث خطأ أثناء استيراد أنواع التوظيف" });
            }
        }
    }
}