using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;

namespace OrderFlowCore.Application.Services
{
    public class QualificationService : IQualificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        public QualificationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<QualificationDto>>> GetAllAsync()
        {
            try
            {
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();
                return ServiceResult<IEnumerable<QualificationDto>>.Success(qualifications);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<QualificationDto>>.Failure($"Error retrieving qualifications: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<QualificationDto>> GetByIdAsync(int id)
        {
            try
            {
                var qualification = await _unitOfWork.Qualifications.GetByIdAsync(id);
                if (qualification == null)
                    return ServiceResult<QualificationDto>.Failure("Qualification not found");
                    
                return ServiceResult<QualificationDto>.Success(qualification);
            }
            catch (Exception ex)
            {
                return ServiceResult<QualificationDto>.Failure($"Error retrieving qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(QualificationDto dto)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Qualification created successfully");
                else
                    return ServiceResult.Failure("Failed to create qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(QualificationDto dto)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Qualification updated successfully");
                else
                    return ServiceResult.Failure("Failed to update qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                if (result)
                    return ServiceResult.Success("Qualification deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting qualification: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
        {
            try
            {
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("المؤهلات");

                // Headers in Arabic
                worksheet.Cells[1, 1].Value = "اسم المؤهل";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 1])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Data rows
                int row = 2;
                foreach (var qualification in qualifications)
                {
                    worksheet.Cells[row, 1].Value = qualification.Name;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var excelData = package.GetAsByteArray();
                return ServiceResult<byte[]>.Success(excelData, "تم تصدير المؤهلات بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تصدير المؤهلات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();

                        // Validate required fields
                        if (string.IsNullOrEmpty(name))
                        {
                            errors.Add($"الصف {row}: اسم المؤهل مطلوب");
                            continue;
                        }

                        // Check if qualification already exists
                        var exists = await _unitOfWork.Qualifications.ExistsAsync(name);
                        if (exists)
                        {
                            errors.Add($"الصف {row}: مؤهل بالاسم {name} موجود بالفعل");
                            continue;
                        }

                        var qualificationDto = new QualificationDto
                        {
                            Name = name
                        };

                        var createResult = await _unitOfWork.Qualifications.CreateAsync(qualificationDto);
                        if (createResult)
                        {
                            importedCount++;
                        }
                        else
                        {
                            errors.Add($"الصف {row}: فشل في إضافة المؤهل {name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                var message = $"تم استيراد {importedCount} مؤهل بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                var finalResult = ServiceResult<int>.Success(importedCount, message);
                finalResult.Errors = errors;
                return finalResult;
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في استيراد المؤهلات: {ex.Message}");
            }
        }
    }
}