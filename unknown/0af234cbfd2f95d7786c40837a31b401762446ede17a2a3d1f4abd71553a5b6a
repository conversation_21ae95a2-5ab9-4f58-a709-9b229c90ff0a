using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories;

public interface IJobTypeRepository
{
    Task<IEnumerable<JobTypeDto>> GetAllAsync();
    Task<JobTypeDto?> GetByIdAsync(int id);
    Task<bool> CreateAsync(JobTypeDto dto);
    Task<bool> UpdateAsync(JobTypeDto dto);
    Task<bool> DeleteAsync(int id);
    Task<bool> ExistsAsync(int id);
    Task<bool> ExistsAsync(string name, int? excludeId = null);
}
