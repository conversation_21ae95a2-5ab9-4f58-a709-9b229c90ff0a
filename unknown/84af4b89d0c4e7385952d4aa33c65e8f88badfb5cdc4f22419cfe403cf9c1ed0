@model UserProfileViewModel
@{
    ViewData["Title"] = "الملف الشخصي";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <div>
                    <h1 class="page-title">الملف الشخصي</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">الملف الشخصي</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8 col-md-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-info text-white">
                    <div class="d-flex align-items-center">
                        <div class="profile-avatar me-3">
                            <i class="fas fa-user-circle fa-3x"></i>
                        </div>
                        <div>
                            <h4 class="mb-0">الملف الشخصي</h4>
                            <small>معلومات المستخدم الشخصية</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="profile-info-card mb-4">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-user me-2"></i>اسم المستخدم
                                    </div>
                                    <div class="info-value">@Model.Username</div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-envelope me-2"></i>البريد الإلكتروني
                                    </div>
                                    <div class="info-value">
                                        @if (!string.IsNullOrEmpty(Model.Email))
                                        {
                                            <a href="mailto:@Model.Email" class="text-decoration-none">@Model.Email</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">غير محدد</span>
                                        }
                                    </div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-phone me-2"></i>رقم الهاتف
                                    </div>
                                    <div class="info-value">
                                        @if (!string.IsNullOrEmpty(Model.Phone))
                                        {
                                            <a href="tel:@Model.Phone" class="text-decoration-none">@Model.Phone</a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">غير محدد</span>
                                        }
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="profile-info-card mb-4">
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-user-tag me-2"></i>الدور
                                    </div>
                                    <div class="info-value">
                                        <span class="badge bg-primary">@Model.Role</span>
                                    </div>
                                </div>
                                
                               @*  <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-calendar-alt me-2"></i>تاريخ الإنشاء
                                    </div>
                                    <div class="info-value">@Model.CreatedAt.ToString("dd/MM/yyyy")</div>
                                </div>
                                
                                <div class="info-item">
                                    <div class="info-label">
                                        <i class="fas fa-clock me-2"></i>آخر تحديث
                                    </div>
                                    <div class="info-value">
                                        @if (Model.UpdatedAt.HasValue)
                                        {
                                            @Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")
                                        }
                                        else
                                        {
                                            <span class="text-muted">لم يتم التحديث بعد</span>
                                        }
                                    </div>
                                </div> *@
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="d-flex justify-content-center gap-3">
                        <a asp-action="EditProfile" class="btn btn-warning btn-lg">
                            <i class="fas fa-edit me-2"></i>تعديل الملف الشخصي
                        </a>
                        <a asp-action="ChangePassword" class="btn btn-secondary btn-lg">
                            <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                        </a>
                        <a href="@Url.Action("Index", "Dashboard")" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-arrow-left me-2"></i>العودة للوحة التحكم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
    // إخفاء رسائل التنبيه تلقائياً
    setTimeout(function() {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
</script> 