using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Data
{
    public class EmploymentTypeRepository : IEmploymentTypeRepository
    {
        private readonly ApplicationDbContext _context;

        public EmploymentTypeRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<EmploymentTypeDto>> GetAllAsync()
        {
            var employmentTypes = await _context.EmploymentTypes
                .AsNoTracking()
                .OrderBy(et => et.Name)
                .ToListAsync();

            return employmentTypes.Select(MapToDto);
        }

        public async Task<EmploymentTypeDto?> GetByIdAsync(int id)
        {
            var employmentType = await _context.EmploymentTypes
                .FirstOrDefaultAsync(et => et.Id == id);

            return employmentType != null ? MapToDto(employmentType) : null;
        }

        public async Task<bool> CreateAsync(EmploymentTypeDto employmentTypeDto)
        {
            var employmentType = MapToEntity(employmentTypeDto);
            await _context.EmploymentTypes.AddAsync(employmentType);
            
            return true;
        }

        public async Task<bool> UpdateAsync(EmploymentTypeDto employmentTypeDto)
        {
            var employmentType = await _context.EmploymentTypes.FindAsync(employmentTypeDto.Id);
            if (employmentType == null)
                return false;

            employmentType.Name = employmentTypeDto.Name;
            
            return true;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var employmentType = await _context.EmploymentTypes.FindAsync(id);
            if (employmentType == null)
                return false;

            _context.EmploymentTypes.Remove(employmentType);
            
            return true;
        }

        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.EmploymentTypes.AnyAsync(et => et.Id == id);
        }

        public async Task<bool> ExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.EmploymentTypes.Where(et => et.Name == name);

            if (excludeId.HasValue)
            {
                query = query.Where(et => et.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        private static EmploymentTypeDto MapToDto(EmploymentType employmentType)
        {
            return new EmploymentTypeDto
            {
                Id = employmentType.Id,
                Name = employmentType.Name,
            };
        }

        private static EmploymentType MapToEntity(EmploymentTypeDto employmentTypeDto)
        {
            return new EmploymentType
            {
                Id = employmentTypeDto.Id,
                Name = employmentTypeDto.Name,
            };
        }
    }
} 