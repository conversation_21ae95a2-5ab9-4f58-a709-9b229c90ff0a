using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Admin, UserRole.Manager)]
    public class JobTypeController : Controller
    {
        private readonly IJobTypeService _jobTypeService;
        
        private readonly ILogger<JobTypeController> _logger;

        public JobTypeController(IJobTypeService jobTypeService, ILogger<JobTypeController> logger)
        {
            _jobTypeService = jobTypeService;
            
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var result = await _jobTypeService.GetAllAsync();
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction("Index", "Home");
            }
            
            return View(result.Data);
        }

        public IActionResult Create()
        {
            return View(new JobTypeDto());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(JobTypeDto jobType)
        {
            if (!ModelState.IsValid)
            {
                return View(jobType);
            }

            var result = await _jobTypeService.CreateAsync(jobType);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }

            return View(jobType);
        }

        public async Task<IActionResult> Edit(int id)
        {
            var result = await _jobTypeService.GetByIdAsync(id);
            if (!result.IsSuccess)
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(JobTypeDto jobType)
        {
            if (ModelState.IsValid)
            {
                var result = await _jobTypeService.UpdateAsync(jobType);
                if (result.IsSuccess)
                {
                    TempData["SuccessMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                }
            }
            return View(jobType);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            var result = await _jobTypeService.DeleteAsync(id);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportToExcel()
        {
            try
            {
                var result = await _jobTypeService.ExportToExcelAsync();
                if (result.IsSuccess)
                {
                    var fileName = $"أنواع_الوظائف_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = result.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting job types to Excel");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير أنواع الوظائف";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportFromExcel(IFormFile excelFile)
        {
            try
            {
                if (excelFile == null || excelFile.Length == 0)
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel" });
                }

                if (!excelFile.FileName.EndsWith(".xlsx") && !excelFile.FileName.EndsWith(".xls"))
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)" });
                }

                using var stream = excelFile.OpenReadStream();
                var result = await _jobTypeService.ImportFromExcelAsync(stream);

                if (result.IsSuccess)
                {
                    return Json(new {
                        success = true,
                        message = result.Message,
                        importedCount = result.Data,
                        errors = result.Errors
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error importing job types from Excel");
                return Json(new { success = false, message = "حدث خطأ أثناء استيراد أنواع الوظائف" });
            }
        }
    }
}