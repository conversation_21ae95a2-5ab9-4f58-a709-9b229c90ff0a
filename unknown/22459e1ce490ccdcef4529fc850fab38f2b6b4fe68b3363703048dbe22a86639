using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;
using System.IO;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IEmploymentTypeService
{
    Task<ServiceResult<IEnumerable<EmploymentTypeDto>>> GetAllAsync();
    Task<ServiceResult<EmploymentTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(EmploymentTypeDto dto);
    Task<ServiceResult> UpdateAsync(EmploymentTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);

    // Import/Export functionality
    Task<ServiceResult<byte[]>> ExportToExcelAsync();
    Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream);
}
