using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;

namespace OrderFlowCore.Application.Services
{
    public class EmploymentTypeService : IEmploymentTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public EmploymentTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<EmploymentTypeDto>>> GetAllAsync()
        {
            try
            {
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                return ServiceResult<IEnumerable<EmploymentTypeDto>>.Success(employmentTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<EmploymentTypeDto>>.Failure($"خطأ في استرجاع أنواع التوظيف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<EmploymentTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var employmentType = await _unitOfWork.EmploymentTypes.GetByIdAsync(id);
                if (employmentType == null)
                    return ServiceResult<EmploymentTypeDto>.Failure("Employment type not found");
                    
                return ServiceResult<EmploymentTypeDto>.Success(employmentType);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmploymentTypeDto>.Failure($"Error retrieving employment type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(EmploymentTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم إنشاء نوع التوظيف بنجاح");
                else
                    return ServiceResult.Failure("فشل في إنشاء نوع التوظيف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إنشاء نوع التوظيف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(EmploymentTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Employment type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update employment type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating employment type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.EmploymentTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                if (result)
                    return ServiceResult.Success("تم حذف نوع التوظيف بنجاح");
                else
                    return ServiceResult.Failure("فشل في حذف نوع التوظيف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في حذف نوع التوظيف: {ex.Message}");
            }
        }

        public async Task<bool> ExistsAsync(int id) => await _unitOfWork.EmploymentTypes.ExistsAsync(id);

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
        {
            try
            {
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("أنواع التوظيف");

                // Headers in Arabic
                worksheet.Cells[1, 1].Value = "نوع التوظيف";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 1])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Data rows
                int row = 2;
                foreach (var employmentType in employmentTypes)
                {
                    worksheet.Cells[row, 1].Value = employmentType.Name;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var excelData = package.GetAsByteArray();
                return ServiceResult<byte[]>.Success(excelData, "تم تصدير أنواع التوظيف بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تصدير أنواع التوظيف: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();

                        // Validate required fields
                        if (string.IsNullOrEmpty(name))
                        {
                            errors.Add($"الصف {row}: اسم نوع التوظيف مطلوب");
                            continue;
                        }

                        // Check if employment type already exists
                        var exists = await _unitOfWork.EmploymentTypes.ExistsAsync(name);
                        if (exists)
                        {
                            errors.Add($"الصف {row}: نوع توظيف بالاسم {name} موجود بالفعل");
                            continue;
                        }

                        var employmentTypeDto = new EmploymentTypeDto
                        {
                            Name = name
                        };

                        var createResult = await _unitOfWork.EmploymentTypes.CreateAsync(employmentTypeDto);
                        if (createResult)
                        {
                            importedCount++;
                        }
                        else
                        {
                            errors.Add($"الصف {row}: فشل في إضافة نوع التوظيف {name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                var message = $"تم استيراد {importedCount} نوع توظيف بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                var finalResult = ServiceResult<int>.Success(importedCount, message);
                finalResult.Errors = errors;
                return finalResult;
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في استيراد أنواع التوظيف: {ex.Message}");
            }
        }
    }
}