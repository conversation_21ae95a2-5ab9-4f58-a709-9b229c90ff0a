@model OrderFlowCore.Web.ViewModels.EmployeeViewModel

@{
    ViewData["Title"] = "تعديل بيانات الموظف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">الموظفين</a></li>
                        <li class="breadcrumb-item active">تعديل بيانات الموظف</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    <i class="fas fa-user-edit me-2"></i>تعديل بيانات الموظف
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات الموظف: @Model.Name
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" novalidate>
                        <input asp-for="Id" type="hidden" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">
                                        <i class="fas fa-user me-1"></i>اسم الموظف <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم الموظف" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Job" class="form-label">
                                        <i class="fas fa-briefcase me-1"></i>الوظيفة <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Job" asp-items="Model.JobTypes" class="form-select">
                                        <option value="">اختر الوظيفة</option>
                                    </select>
                                    <span asp-validation-for="Job" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeNumber" class="form-label">
                                        <i class="fas fa-id-badge me-1"></i>رقم الموظف
                                    </label>
                                    <input asp-for="EmployeeNumber" class="form-control" placeholder="أدخل رقم الموظف" />
                                    <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CivilNumber" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>الرقم المدني <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="CivilNumber" class="form-control" placeholder="أدخل الرقم المدني" readonly />
                                    <small class="form-text text-muted">لا يمكن تعديل الرقم المدني</small>
                                    <span asp-validation-for="CivilNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Nationality" class="form-label">
                                        <i class="fas fa-flag me-1"></i>الجنسية <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Nationality" asp-items="Model.Nationalities" class="form-select">
                                        <option value="">اختر الجنسية</option>
                                    </select>
                                    <span asp-validation-for="Nationality" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Mobile" class="form-label">
                                        <i class="fas fa-phone me-1"></i>رقم الهاتف
                                    </label>
                                    <input asp-for="Mobile" class="form-control" placeholder="أدخل رقم الهاتف" />
                                    <span asp-validation-for="Mobile" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmploymentType" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>نوع التوظيف <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="EmploymentType" asp-items="Model.EmploymentTypes" class="form-select">
                                        <option value="">اختر نوع التوظيف</option>
                                    </select>
                                    <span asp-validation-for="EmploymentType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Qualification" class="form-label">
                                        <i class="fas fa-graduation-cap me-1"></i>المؤهل <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Qualification" asp-items="Model.Qualifications" class="form-select">
                                        <option value="">اختر المؤهل</option>
                                    </select>
                                    <span asp-validation-for="Qualification" class="text-danger"></span>
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> الرقم المدني لا يمكن تعديله لأسباب أمنية. إذا كان هناك خطأ في الرقم المدني، يرجى التواصل مع الإدارة.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                                    </a>

                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>حفظ التعديلات
                                    </button>

                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <script>
        // Auto-format mobile number input
        document.getElementById('Mobile').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 8) {
                value = value.substring(0, 8); // Limit to 8 digits
            }
            e.target.value = value;
        });

        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const name = document.getElementById('Name').value;

            if (!name.trim()) {
                e.preventDefault();
                alert('يرجى إدخال اسم الموظف');
                document.getElementById('Name').focus();
                return;
            }

            // Confirm before saving changes
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
                return;
            }
        });

        // Highlight changed fields
        const originalValues = {};
        document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"]').forEach(input => {
            if (input.id !== 'CivilNumber') { // Skip readonly field
                originalValues[input.id] = input.value;

                input.addEventListener('input', function() {
                    if (this.value !== originalValues[this.id]) {
                        this.classList.add('border-warning');
                        this.classList.remove('border-success');
                    } else {
                        this.classList.remove('border-warning');
                        this.classList.add('border-success');
                    }
                });
            }
        });
    </script>
}
