using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Application.Services;

public class PasswordResetService : IPasswordResetService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IEmailService _emailService;
    private readonly IValidationService _validationService;
    private readonly ILogger<PasswordResetService> _logger;
    private readonly IAuthService _authService;

    public PasswordResetService(
        IUnitOfWork unitOfWork,
        IEmailService emailService,
        IValidationService validationService,
        IAuthService authService,
        ILogger<PasswordResetService> logger)
    {
        _unitOfWork = unitOfWork;
        _emailService = emailService;
        _validationService = validationService;
        _authService = authService;
        _logger = logger;
    }

    public async Task<ServiceResult> RequestResetAsync(string usernameOrEmail)
    {
        try
        {
            var user = await FindUserAsync(usernameOrEmail);
            if (user == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            if (string.IsNullOrEmpty(user.Email))
                return ServiceResult.Failure("لا يوجد بريد إلكتروني مرتبط بالحساب");

            var otp = GenerateOtp();
            var entity = await _unitOfWork.Users.GetUserByIdAsync(user.Id);
            if (entity == null) return ServiceResult.Failure("المستخدم غير موجود");

            entity.ResetToken = otp;
            entity.ResetTokenExpiry = DateTime.UtcNow.AddMinutes(10);
            await _unitOfWork.Users.UpdateAsync(entity);
            await _unitOfWork.SaveChangesAsync();

            var subject = "رمز استعادة كلمة المرور";
            string body = GenerateResetEmailContent(user, otp);

            var sent = await _emailService.SendAsync(user.Email!, subject, body, true);
            if (!sent)
                return ServiceResult.Failure("تعذر إرسال البريد الإلكتروني. تحقق من إعدادات البريد.");

            return ServiceResult.Success("تم إرسال رمز التحقق إلى بريدك الإلكتروني");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password reset request for {User}", usernameOrEmail);
            return ServiceResult.Failure("حدث خطأ أثناء طلب الاستعادة");
        }
    }


    public async Task<ServiceResult> VerifyOtpAsync(string usernameOrEmail, string otp, string newPassword)
    {
        try
        {
            var user = await FindUserAsync(usernameOrEmail);
            if (user == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            var entity = await _unitOfWork.Users.GetUserByIdAsync(user.Id);
            if (entity == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            if (string.IsNullOrEmpty(entity.ResetToken) || entity.ResetTokenExpiry == null)
                return ServiceResult.Failure("لا يوجد رمز تحقق صالح");

            if (!string.Equals(entity.ResetToken, otp, StringComparison.Ordinal))
                return ServiceResult.Failure("رمز التحقق غير صحيح");

            if (DateTime.UtcNow > entity.ResetTokenExpiry)
                return ServiceResult.Failure("انتهت صلاحية رمز التحقق");

            var pwdValidation = _validationService.ValidatePassword(newPassword);
            if (!pwdValidation.IsSuccess)
                return pwdValidation;

            entity.Password = _authService.HashPassword(newPassword);
            entity.ResetToken = null;
            entity.ResetTokenExpiry = null;
            await _unitOfWork.Users.UpdateAsync(entity);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم تغيير كلمة المرور بنجاح");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying OTP for {User}", usernameOrEmail);
            return ServiceResult.Failure("حدث خطأ أثناء التحقق من الرمز");
        }
    }

    private static string GenerateOtp()
    {
        // 6-digit numeric code
        var bytes = RandomNumberGenerator.GetBytes(4);
        var value = BitConverter.ToUInt32(bytes, 0) % 1000000u;
        return value.ToString("D6");
    }

    private async Task<UserDto?> FindUserAsync(string usernameOrEmail)
    {
        if (string.IsNullOrWhiteSpace(usernameOrEmail))
            return null;

        // Check if it's an email
        var isEmail = _validationService.ValidateEmail(usernameOrEmail).IsSuccess;

        if (isEmail)
        {
            var entity = await _unitOfWork.Users.GetByEmailAsync(usernameOrEmail);
            return entity == null ? null : new UserDto
            {
                Id = entity.Id,
                Username = entity.Username,
                Email = entity.Email,
                Phone = entity.Phone,
                UserRole = entity.UserRole,
                RoleType = entity.RoleType,
                IsActive = entity.IsActive
            };
        }

        // Otherwise, search by username
        return await _unitOfWork.Users.GetByUsernameAsync(usernameOrEmail);
    }

    private static string GenerateResetEmailContent(UserDto user, string otp)
    {
        return $@"
            <table style='max-width:600px;margin:0 auto;border:1px solid #e0e0e0;border-radius:8px;font-family:Arial,sans-serif;background-color:#ffffff;'>
                <tr>
                    <td style='background-color:#4CAF50;color:#ffffff;padding:15px;text-align:center;font-size:20px;font-weight:bold;border-top-left-radius:8px;border-top-right-radius:8px;'>
                        استعادة كلمة المرور
                    </td>
                </tr>
                <tr>
                    <td style='padding:20px;color:#333333;font-size:16px;line-height:1.6;'>
                        مرحباً {user.Username ?? "المستخدم"},
                        <br><br>
                        لقد طلبت استعادة كلمة المرور الخاصة بك. استخدم رمز التحقق أدناه لإكمال العملية.
                        <br><br>
                        <div style='text-align:center;margin:20px 0;'>
                            <span style='display:inline-block;background-color:#4CAF50;color:#ffffff;font-size:22px;font-weight:bold;padding:12px 24px;border-radius:6px;letter-spacing:2px;'>
                                {otp}
                            </span>
                        </div>
                        <br>
                        هذا الرمز صالح لمدة <b>10 دقائق</b>.
                        <br><br>
                        إذا لم تطلب استعادة كلمة المرور، يمكنك تجاهل هذا البريد.
                    </td>
                </tr>
                <tr>
                    <td style='background-color:#f8f8f8;color:#777777;padding:10px;text-align:center;font-size:12px;border-bottom-left-radius:8px;border-bottom-right-radius:8px;'>
                        &copy; {DateTime.Now.Year} جميع الحقوق محفوظة
                    </td>
                </tr>
            </table>";
    }
}

