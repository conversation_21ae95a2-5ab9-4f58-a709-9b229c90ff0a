using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs;

public class NotificationDto
{
    public int Id { get; set; }
    
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;
    
    [Required]
    [StringLength(500)]
    public string Message { get; set; } = string.Empty;
    
    public string? ActionUrl { get; set; }
    
    public bool IsRead { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public string TimeAgo { get; set; } = string.Empty;
}

public class NotificationResponseDto
{
    public bool Success { get; set; }
    public string? Error { get; set; }
    public int Count { get; set; }
    public List<NotificationDto> Notifications { get; set; } = new();
}

public class OrderNotificationDto
{
    public int OrderCount { get; set; }
    public string UserRole { get; set; } = string.Empty;
    public string? RoleType { get; set; }
    public string NotificationMessage { get; set; } = string.Empty;
}
