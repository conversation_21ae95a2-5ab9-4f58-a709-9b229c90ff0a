﻿// <auto-generated> This file has been auto generated by EF Core Power Tools. </auto-generated>
#nullable disable
using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure;

public partial class ApplicationDbContext : DbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }
    public virtual DbSet<User> Users { get; set; }

    public virtual DbSet<AutoRouting> AutoRoutings { get; set; }

    public virtual DbSet<Department> Departments { get; set; }

    public virtual DbSet<DirectRouting> DirectRoutings { get; set; }

    public virtual DbSet<Employee> Employees { get; set; }

    public virtual DbSet<EmploymentType> EmploymentTypes { get; set; }

    public virtual DbSet<Nationality> Nationalities { get; set; }

    public virtual DbSet<JobType> JobTypes { get; set; }

    public virtual DbSet<OrdersTable> OrdersTables { get; set; }

    public virtual DbSet<OrdersType> OrdersTypes { get; set; }

    public virtual DbSet<PathsTable> PathsTables { get; set; }

    public virtual DbSet<Qualification> Qualifications { get; set; }

    public virtual DbSet<SupervisorsFollowUp> SupervisorsFollowUps { get; set; }


    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.UseCollation("Arabic_CI_AS");
        modelBuilder.Entity<User>(entity =>
        {
            entity.HasIndex(e => e.Email, "UQ_login_email")
                .IsUnique();

            entity.HasIndex(e => e.Phone, "UQ_login_phone")
                .IsUnique();
        });

        modelBuilder.Entity<Employee>(entity =>
        {
            entity.HasIndex(e => e.CivilNumber)
                .IsUnique();
        });

        modelBuilder.Entity<SupervisorsFollowUp>(entity =>
            entity.HasKey(e=> new {e.SupervisorId, e.CivilRecord })
        );

        modelBuilder.Entity<OrdersTable>().Property(o => o.OrderStatus)
               .HasConversion<string>() 
               .HasMaxLength(50);

        modelBuilder.Entity<Department>().Property(o => o.AssistantManagerId)
               .HasConversion<string>()
               .HasMaxLength(50);

    }
}