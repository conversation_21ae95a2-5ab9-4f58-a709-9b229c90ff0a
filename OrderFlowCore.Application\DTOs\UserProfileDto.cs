using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.DTOs
{
    public class UserProfileDto
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        // New role system properties
        [Display(Name = "الدور")]
        public UserRole UserRole { get; set; } = UserRole.DirectManager;

        [Display(Name = "تخصص الدور")]
        public string? RoleType { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "آخر تحديث")]
        public DateTime? UpdatedAt { get; set; }
    }
} 