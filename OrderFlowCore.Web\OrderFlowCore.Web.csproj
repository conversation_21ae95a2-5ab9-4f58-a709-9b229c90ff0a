﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Resources\Templates\Form.docx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Resources\Templates\Form.docx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\OrderFlowCore.Application\OrderFlowCore.Application.csproj" />
    <ProjectReference Include="..\OrderFlowCore.Infrastructure\OrderFlowCore.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="wwwroot\js\HRCoordinator\" />
    <Folder Include="wwwroot\order_printed\" />
  </ItemGroup>

</Project>
