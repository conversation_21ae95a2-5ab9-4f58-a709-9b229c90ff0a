using Microsoft.Extensions.Configuration;
using Moq;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using Xunit;

namespace OrderFlowCore.Tests.Services
{
    public class HRManagerServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<ISupervisorService> _mockSupervisorService;
        private readonly Mock<IOrderRepository> _mockOrderRepository;
        private readonly HRManagerService _hrManagerService;

        public HRManagerServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockSupervisorService = new Mock<ISupervisorService>();
            _mockOrderRepository = new Mock<IOrderRepository>();

            _mockUnitOfWork.Setup(u => u.Orders).Returns(_mockOrderRepository.Object);

            _hrManagerService = new HRManagerService(
                _mockUnitOfWork.Object,
                _mockConfiguration.Object,
                _mockSupervisorService.Object);
        }

        [Fact]
        public async Task GetHRManagerOrdersAsync_ShouldReturnOrdersWithStatusD()
        {
            // Arrange
            var orders = new List<OrdersTable>
            {
                new OrdersTable
                {
                    Id = 1,
                    EmployeeName = "أحمد محمد",
                    Department = "قسم الموارد البشرية",
                    OrderDate = DateTime.Now,
                    OrderStatus = OrderStatus.D
                },
                new OrdersTable
                {
                    Id = 2,
                    EmployeeName = "فاطمة علي",
                    Department = "قسم المالية",
                    OrderDate = DateTime.Now.AddDays(-1),
                    OrderStatus = OrderStatus.D
                }
            };

            _mockOrderRepository.Setup(r => r.GetOrdersByStatusAsync(OrderStatus.D))
                .ReturnsAsync(orders);

            // Act
            var result = await _hrManagerService.GetHRManagerOrdersAsync();

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(2, result.Data.Count);
            Assert.Equal("أحمد محمد", result.Data.First().EmployeeName);
        }

        [Fact]
        public async Task ApproveOrderAsync_ShouldUpdateOrderStatusToAccepted()
        {
            // Arrange
            var order = new OrdersTable
            {
                Id = 1,
                OrderStatus = OrderStatus.D,
                EmployeeName = "أحمد محمد"
            };

            _mockOrderRepository.Setup(r => r.GetByIdAsync(1))
                .ReturnsAsync(order);

            _mockOrderRepository.Setup(r => r.UpdateAsync(It.IsAny<OrdersTable>()))
                .ReturnsAsync(1);

            _mockUnitOfWork.Setup(u => u.SaveChangesAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _hrManagerService.ApproveOrderAsync(1, "مدير الموارد البشرية", false);

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(OrderStatus.Accepted, order.OrderStatus);
            Assert.Contains("مدير الموارد البشرية", order.HRManagerApproval);
        }

        [Fact]
        public async Task RejectOrderAsync_ShouldUpdateOrderStatusToCancelledByManager()
        {
            // Arrange
            var order = new OrdersTable
            {
                Id = 1,
                OrderStatus = OrderStatus.D,
                EmployeeName = "أحمد محمد"
            };

            _mockOrderRepository.Setup(r => r.GetByIdAsync(1))
                .ReturnsAsync(order);

            _mockOrderRepository.Setup(r => r.UpdateAsync(It.IsAny<OrdersTable>()))
                .ReturnsAsync(1);

            _mockUnitOfWork.Setup(u => u.SaveChangesAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _hrManagerService.RejectOrderAsync(1, "سبب الإلغاء", "مدير الموارد البشرية");

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(OrderStatus.CancelledByManager, order.OrderStatus);
            Assert.Equal("سبب الإلغاء", order.CancellationReason);
        }

        [Fact]
        public async Task ReturnOrderAsync_ShouldUpdateOrderStatusToReturnedByManager()
        {
            // Arrange
            var order = new OrdersTable
            {
                Id = 1,
                OrderStatus = OrderStatus.D,
                EmployeeName = "أحمد محمد"
            };

            _mockOrderRepository.Setup(r => r.GetByIdAsync(1))
                .ReturnsAsync(order);

            _mockOrderRepository.Setup(r => r.UpdateAsync(It.IsAny<OrdersTable>()))
                .ReturnsAsync(1);

            _mockUnitOfWork.Setup(u => u.SaveChangesAsync())
                .ReturnsAsync(1);

            // Act
            var result = await _hrManagerService.ReturnOrderAsync(1, "سبب الإعادة", "مدير الموارد البشرية");

            // Assert
            Assert.True(result.IsSuccess);
            Assert.Equal(OrderStatus.ReturnedByManager, order.OrderStatus);
            Assert.Equal("سبب الإعادة", order.CancellationReason);
        }

        [Fact]
        public async Task GetAvailableStatusesAsync_ShouldReturnListOfStatuses()
        {
            // Act
            var result = await _hrManagerService.GetAvailableStatusesAsync();

            // Assert
            Assert.True(result.IsSuccess);
            Assert.True(result.Data.Count > 0);
            Assert.Contains(result.Data, s => s.Text.Contains("مقبول"));
        }

        [Fact]
        public async Task ApproveOrderAsync_WithInvalidOrderId_ShouldReturnFailure()
        {
            // Arrange
            _mockOrderRepository.Setup(r => r.GetByIdAsync(999))
                .ReturnsAsync((OrdersTable)null);

            // Act
            var result = await _hrManagerService.ApproveOrderAsync(999, "مدير الموارد البشرية", false);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("لم يتم العثور على الطلب", result.Message);
        }

        [Fact]
        public async Task RejectOrderAsync_WithEmptyReason_ShouldReturnFailure()
        {
            // Act
            var result = await _hrManagerService.RejectOrderAsync(1, "", "مدير الموارد البشرية");

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Equal("يرجى إدخال سبب الإلغاء", result.Message);
        }
    }
}
