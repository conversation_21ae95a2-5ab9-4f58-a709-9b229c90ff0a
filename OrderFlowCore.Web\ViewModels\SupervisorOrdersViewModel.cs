using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Web.ViewModels
{
    public class SupervisorOrdersViewModel
    {
        // Dropdown for order numbers
        public List<SelectListItem> OrderNumbers { get; set; } = new List<SelectListItem>();
        public int? SelectedOrderId { get; set; }

        // Follow-up records
        public List<SupervisorsFollowUpDto> FollowUpRecords { get; set; } = new List<SupervisorsFollowUpDto>();
    }
} 