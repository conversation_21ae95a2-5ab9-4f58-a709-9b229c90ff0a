using FluentAssertions;
using OrderFlowCore.Application.Services;
using Xunit;

namespace OrderFlowCore.Tests.Services;

public class ValidationServiceTests
{
    private readonly ValidationService _validationService;

    public ValidationServiceTests()
    {
        _validationService = new ValidationService();
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("@domain.com", false)]
    [InlineData("user@", false)]
    [InlineData("", true)] // Empty email is considered valid (optional)
    [InlineData(null, true)] // Null email is considered valid (optional)
    public void ValidateEmail_ShouldReturnCorrectResult(string email, bool expectedIsSuccess)
    {
        // Act
        var result = _validationService.ValidateEmail(email);

        // Assert
        result.IsSuccess.Should().Be(expectedIsSuccess);
    }

    [Theory]
    [InlineData("password123", 6, true)]
    [InlineData("12345", 6, false)]
    [InlineData("", 6, false)]
    [InlineData(null, 6, false)]
    [InlineData("longpassword", 8, true)]
    [InlineData("short", 8, false)]
    public void ValidatePassword_ShouldReturnCorrectResult(string password, int minLength, bool expectedIsSuccess)
    {
        // Act
        var result = _validationService.ValidatePassword(password, minLength);

        // Assert
        result.IsSuccess.Should().Be(expectedIsSuccess);
    }

    [Theory]
    [InlineData("1234567890", true)]
    [InlineData("************", true)]
    [InlineData("(*************", true)]
    [InlineData("123", false)]
    [InlineData("12345678901234567890", false)]
    [InlineData("", true)] // Empty phone is considered valid (optional)
    [InlineData(null, true)] // Null phone is considered valid (optional)
    public void ValidatePhone_ShouldReturnCorrectResult(string phone, bool expectedIsSuccess)
    {
        // Act
        var result = _validationService.ValidatePhone(phone);

        // Assert
        result.IsSuccess.Should().Be(expectedIsSuccess);
    }

    [Theory]
    [InlineData("validuser", true)]
    [InlineData("user123", true)]
    [InlineData("user_name", true)]
    [InlineData("user-name", true)]
    [InlineData("مستخدم", true)] // Arabic username
    [InlineData("ab", false)] // Too short
    [InlineData("", false)] // Empty
    [InlineData(null, false)] // Null
    public void ValidateUsername_ShouldReturnCorrectResult(string username, bool expectedIsSuccess)
    {
        // Act
        var result = _validationService.ValidateUsername(username);

        // Assert
        result.IsSuccess.Should().Be(expectedIsSuccess);
    }

    [Theory]
    [InlineData("Super Admin", true)]
    [InlineData("regular_user", false)]
    [InlineData("admin", false)]
    public void IsProtectedUser_ShouldReturnCorrectResult(string username, bool expectedIsProtected)
    {
        // Act
        var result = _validationService.IsProtectedUser(username);

        // Assert
        result.Should().Be(expectedIsProtected);
    }

    [Theory]
    [InlineData("password123", "password123", true)]
    [InlineData("password123", "different", false)]
    [InlineData("", "", true)]
    public void ValidatePasswordConfirmation_ShouldReturnCorrectResult(string password, string confirmPassword, bool expectedIsSuccess)
    {
        // Act
        var result = _validationService.ValidatePasswordConfirmation(password, confirmPassword);

        // Assert
        result.IsSuccess.Should().Be(expectedIsSuccess);
    }
}
