using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class DropdownDataDto
    {
        public List<DropdownItemDto> Departments { get; set; } = new();
        public List<DropdownItemDto> OrderTypes { get; set; } = new();
        public List<DropdownItemDto> JobTitles { get; set; } = new();
        public List<DropdownItemDto> Nationalities { get; set; } = new();
        public List<DropdownItemDto> EmploymentTypes { get; set; } = new();
        public List<DropdownItemDto> Qualifications { get; set; } = new();
    }

    public class DropdownItemDto
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }
} 