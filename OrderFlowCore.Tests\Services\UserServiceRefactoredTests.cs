using FluentAssertions;
using Moq;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using Xunit;

namespace OrderFlowCore.Tests.Services;

public class UserServiceRefactoredTests
{
    private readonly Mock<IUnitOfWork> _mockUnitOfWork;
    private readonly Mock<IAuthService> _mockAuthService;
    private readonly Mock<IValidationService> _mockValidationService;
    private readonly Mock<IUserRepository> _mockUserRepository;
    private readonly UserService _userService;

    public UserServiceRefactoredTests()
    {
        _mockUnitOfWork = new Mock<IUnitOfWork>();
        _mockAuthService = new Mock<IAuthService>();
        _mockValidationService = new Mock<IValidationService>();
        _mockUserRepository = new Mock<IUserRepository>();

        _mockUnitOfWork.Setup(x => x.Users).Returns(_mockUserRepository.Object);

        _userService = new UserService(_mockUnitOfWork.Object, _mockAuthService.Object, _mockValidationService.Object);
    }

    [Fact]
    public async Task CreateUserAsync_WithValidData_ShouldSucceed()
    {
        // Arrange
        var userDto = new UserDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Phone = "1234567890",
            Password = "password123",
            UserRole = UserRole.DirectManager
        };

        _mockValidationService.Setup(x => x.ValidateUsername(userDto.Username))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidateEmail(userDto.Email))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidatePhone(userDto.Phone))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidatePassword(userDto.Password, 6))
            .Returns(ServiceResult.Success());

        _mockUserRepository.Setup(x => x.GetByUsernameAsync(userDto.Username))
            .ReturnsAsync((User)null);
        _mockUserRepository.Setup(x => x.GetByEmailAsync(userDto.Email))
            .ReturnsAsync((User)null);

        _mockAuthService.Setup(x => x.HashPassword(userDto.Password))
            .Returns("hashedpassword");

        // Act
        var result = await _userService.CreateUserAsync(userDto);

        // Assert
        result.IsSuccess.Should().BeTrue();
        _mockUserRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }

    [Fact]
    public async Task CreateUserAsync_WithInvalidEmail_ShouldFail()
    {
        // Arrange
        var userDto = new UserDto
        {
            Username = "testuser",
            Email = "invalid-email",
            Password = "password123"
        };

        _mockValidationService.Setup(x => x.ValidateUsername(userDto.Username))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidateEmail(userDto.Email))
            .Returns(ServiceResult.Failure("البريد الإلكتروني غير صحيح"));

        // Act
        var result = await _userService.CreateUserAsync(userDto);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Be("البريد الإلكتروني غير صحيح");
        _mockUserRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Never);
    }

    [Fact]
    public async Task CreateUserAsync_WithExistingUsername_ShouldFail()
    {
        // Arrange
        var userDto = new UserDto
        {
            Username = "existinguser",
            Email = "<EMAIL>",
            Password = "password123"
        };

        var existingUser = new User { Id = 1, Username = "existinguser" };

        _mockValidationService.Setup(x => x.ValidateUsername(userDto.Username))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidateEmail(userDto.Email))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidatePhone(userDto.Phone))
            .Returns(ServiceResult.Success());
        _mockValidationService.Setup(x => x.ValidatePassword(userDto.Password, 6))
            .Returns(ServiceResult.Success());

        _mockUserRepository.Setup(x => x.GetByUsernameAsync(userDto.Username))
            .ReturnsAsync(existingUser);

        // Act
        var result = await _userService.CreateUserAsync(userDto);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Be("Username already exists");
        _mockUserRepository.Verify(x => x.AddAsync(It.IsAny<User>()), Times.Never);
    }

    [Fact]
    public async Task DeleteUserAsync_WithProtectedUser_ShouldFail()
    {
        // Arrange
        var username = "Super Admin";
        var user = new User { Id = 1, Username = username };

        _mockUserRepository.Setup(x => x.GetByUsernameAsync(username))
            .ReturnsAsync(user);
        _mockValidationService.Setup(x => x.IsProtectedUser(username))
            .Returns(true);

        // Act
        var result = await _userService.DeleteUserAsync(username);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Be("Cannot delete protected user");
        _mockUserRepository.Verify(x => x.DeleteAsync(It.IsAny<int>()), Times.Never);
    }

    [Fact]
    public async Task SetUserPasswordAsync_WithValidPassword_ShouldSucceed()
    {
        // Arrange
        var username = "testuser";
        var newPassword = "newpassword123";
        var user = new User { Id = 1, Username = username };

        _mockUserRepository.Setup(x => x.GetByUsernameAsync(username))
            .ReturnsAsync(user);
        _mockValidationService.Setup(x => x.ValidatePassword(newPassword, 6))
            .Returns(ServiceResult.Success());
        _mockAuthService.Setup(x => x.HashPassword(newPassword))
            .Returns("hashedpassword");

        // Act
        var result = await _userService.SetUserPasswordAsync(username, newPassword);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("تم تغيير كلمة المرور بنجاح");
        _mockUserRepository.Verify(x => x.UpdateAsync(user), Times.Once);
        _mockUnitOfWork.Verify(x => x.SaveChangesAsync(), Times.Once);
    }
}
