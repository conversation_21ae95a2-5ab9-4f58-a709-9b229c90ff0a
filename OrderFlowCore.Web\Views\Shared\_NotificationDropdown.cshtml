<div class="dropdown">
    <a class="nav-link dropdown-toggle position-relative" href="#" id="notificationIcon" role="button"
       data-bs-toggle="dropdown" data-bs-auto-close="outside" aria-expanded="false">
        <i class="fas fa-bell fs-5"></i>
        <span class="position-absolute translate-middle badge rounded-pill bg-danger"
              id="notification-badge" style="display: none;">
            0
        </span>
    </a>
    <div class="dropdown-menu dropdown-menu-end notification-dropdown shadow-lg"
         aria-labelledby="notificationIcon" style="width: 350px; max-height: 400px; overflow-y: auto;">
        <div class="dropdown-header d-flex justify-content-between align-items-center bg-light">
            <h6 class="mb-0 text-primary">
                <i class="fas fa-bell me-2"></i>الإشعارات
            </h6>
            <div>
                <form asp-controller="Notification" asp-action="MarkAllAsRead" method="post" class="d-inline">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-sm btn-outline-primary" title="تحديد الكل كمقروء">
                        <i class="fas fa-check-double"></i>
                    </button>
                </form>
            </div>
        </div>
        <div id="notification-list">
            <div class="p-3 text-center">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mb-0 mt-2 text-muted">جاري تحميل الإشعارات...</p>
            </div>
        </div>
    </div>
</div>
