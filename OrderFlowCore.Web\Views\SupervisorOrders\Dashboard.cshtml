@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel

@{
    ViewData["Title"] = "لوحة تحكم مشرف الطلبات";
}

<div class="container-fluid dashboard-container">
    <!-- Welcome Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-2 text-center">
                <i class="fas fa-user-shield fa-4x text-primary"></i>
            </div>
            <div class="col-md-10">
                <h2 class="mb-1">مرحباً، مشرف الطلبات!</h2>
                <p class="mb-1"><i class="fas fa-user-tag me-2"></i>الدور: مشرف الطلبات</p>
                <p class="mb-0"><i class="fas fa-building me-2"></i>قسم: إدارة الطلبات</p>
            </div>
        </div>
    </div>

    <!-- Key Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card primary">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">@(Model?.OrderNumbers?.Count() ?? 0)</div>
                        <div>إجمالي الطلبات</div>
                    </div>
                    <i class="fas fa-file-alt stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card warning">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">0</div>
                        <div>الطلبات المعلقة</div>
                    </div>
                    <i class="fas fa-clock stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card success">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">0</div>
                        <div>طلبات معتمدة</div>
                    </div>
                    <i class="fas fa-check-circle stat-icon"></i>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="stat-card danger">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-number">0</div>
                        <div>تتطلب إجراء</div>
                    </div>
                    <i class="fas fa-exclamation-triangle stat-icon"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Statistics -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-day text-primary mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-primary">0</h4>
                    <p class="text-muted mb-0">معالجة اليوم</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-week text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">0</h4>
                    <p class="text-muted mb-0">معالجة هذا الأسبوع</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-alt text-info mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-info">0</h4>
                    <p class="text-muted mb-0">معالجة هذا الشهر</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Statistics -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-thumbs-up text-success mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-success">0</h4>
                    <p class="text-muted mb-0">طلبات معتمدة</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-circle text-warning mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-warning">0</h4>
                    <p class="text-muted mb-0">تحتاج إجراءات</p>
                </div>
            </div>
        </div>
        <div class="col-lg-4 col-md-6 mb-3">
            <div class="card dashboard-card">
                <div class="card-body text-center">
                    <i class="fas fa-undo text-danger mb-2" style="font-size: 2rem;"></i>
                    <h4 class="text-danger">0</h4>
                    <p class="text-muted mb-0">طلبات مُعادة</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Follow-up Records and Quick Actions -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>سجلات المتابعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span class="fw-bold">إجمالي السجلات</span>
                        <span class="badge bg-primary">@(Model?.FollowUpRecords?.Count() ?? 0)</span>
                    </div>
                    <div class="progress-modern mb-3">
                        <div class="progress-bar bg-primary" style="width: 100%; --progress-color: #007bff; --progress-color-light: #4da3ff;"></div>
                    </div>
                    <div class="d-grid">
                        <a asp-action="FollowUpRecords" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>عرض جميع السجلات
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6 mb-4">
            <div class="card dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>الإجراءات السريعة</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a asp-action="ProcessOrder" class="btn btn-success">
                            <i class="fas fa-play-circle me-2"></i>بدء معالجة الطلبات
                        </a>
                        <a asp-action="FollowUpRecords" class="btn btn-dark">
                            <i class="fas fa-clipboard-list me-2"></i>إدارة السجلات
                        </a>
                        <a asp-action="Index" class="btn btn-info">
                            <i class="fas fa-home me-2"></i>العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card dashboard-card">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0"><i class="fas fa-history me-2"></i>النشاطات الأخيرة</h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker" style="background-color: #28a745;">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="timeline-content">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">مرحباً بك في لوحة التحكم</h6>
                                        <p class="mb-1">يمكنك الآن البدء في معالجة الطلبات وإدارة السجلات</p>
                                        <small class="text-muted">
                                            ابدأ بمعالجة الطلبات أو إدارة سجلات المتابعة
                                        </small>
                                    </div>
                                    <div class="text-end">
                                        <span class="badge" style="background-color: #28a745;">نشط</span>
                                        <small class="text-muted d-block">@DateTime.Now.ToString("dd/MM/yyyy HH:mm")</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }

        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
        }

        .timeline-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        /* Progress bar styling */
        .progress-modern {
            height: 8px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--progress-color), var(--progress-color-light));
            border-radius: 10px;
            transition: width 0.6s ease;
        }
    </style>
}
