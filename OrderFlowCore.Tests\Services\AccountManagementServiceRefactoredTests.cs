using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Core.Entities;
using Xunit;

namespace OrderFlowCore.Tests.Services;

public class AccountManagementServiceRefactoredTests
{
    private readonly Mock<IUserService> _mockUserService;
    private readonly Mock<IRoleService> _mockRoleService;
    private readonly Mock<IValidationService> _mockValidationService;
    private readonly Mock<ILogger<AccountManagementService>> _mockLogger;
    private readonly AccountManagementService _accountManagementService;

    public AccountManagementServiceRefactoredTests()
    {
        _mockUserService = new Mock<IUserService>();
        _mockRoleService = new Mock<IRoleService>();
        _mockValidationService = new Mock<IValidationService>();
        _mockLogger = new Mock<ILogger<AccountManagementService>>();

        _accountManagementService = new AccountManagementService(
            _mockUserService.Object,
            _mockRoleService.Object,
            _mockValidationService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task CreateUserAsync_ShouldDelegateToUserService()
    {
        // Arrange
        var createDto = new UserCreateDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Phone = "**********",
            Password = "password123",
            UserRole = UserRole.DirectManager
        };

        var expectedUserDto = new UserDto
        {
            Username = createDto.Username,
            Email = createDto.Email,
            Phone = createDto.Phone,
            Password = createDto.Password,
            UserRole = createDto.UserRole,
            RoleType = createDto.RoleType
        };

        _mockUserService.Setup(x => x.CreateUserAsync(It.IsAny<UserDto>()))
            .ReturnsAsync(ServiceResult<UserDto>.Success(expectedUserDto));

        // Act
        var result = await _accountManagementService.CreateUserAsync(createDto);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("تم إضافة المستخدم بنجاح");
        
        _mockUserService.Verify(x => x.CreateUserAsync(It.Is<UserDto>(dto =>
            dto.Username == createDto.Username &&
            dto.Email == createDto.Email &&
            dto.Phone == createDto.Phone &&
            dto.Password == createDto.Password &&
            dto.UserRole == createDto.UserRole &&
            dto.RoleType == createDto.RoleType)), Times.Once);
    }

    [Fact]
    public async Task CreateUserAsync_WhenUserServiceFails_ShouldReturnFailure()
    {
        // Arrange
        var createDto = new UserCreateDto
        {
            Username = "testuser",
            Password = "password123"
        };

        _mockUserService.Setup(x => x.CreateUserAsync(It.IsAny<UserDto>()))
            .ReturnsAsync(ServiceResult<UserDto>.Failure("Username already exists"));

        // Act
        var result = await _accountManagementService.CreateUserAsync(createDto);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Be("Username already exists");
    }

    [Fact]
    public async Task UpdateUserAsync_ShouldDelegateToUserService()
    {
        // Arrange
        var editDto = new UserEditDto
        {
            Username = "testuser",
            Email = "<EMAIL>",
            Phone = "**********"
        };

        var existingUserDto = new UserDto
        {
            Id = 1,
            Username = "testuser",
            Email = "<EMAIL>",
            Phone = "**********"
        };

        _mockUserService.Setup(x => x.GetUserByUsernameAsync(editDto.Username))
            .ReturnsAsync(ServiceResult<UserDto>.Success(existingUserDto));
        _mockUserService.Setup(x => x.UpdateUserAsync(It.IsAny<UserDto>()))
            .ReturnsAsync(ServiceResult<UserDto>.Success(existingUserDto));

        // Act
        var result = await _accountManagementService.UpdateUserAsync(editDto);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("تم تحديث المستخدم بنجاح");
        
        _mockUserService.Verify(x => x.UpdateUserAsync(It.Is<UserDto>(dto =>
            dto.Email == editDto.Email &&
            dto.Phone == editDto.Phone)), Times.Once);
    }

    [Fact]
    public async Task DeleteUserAsync_ShouldDelegateToUserService()
    {
        // Arrange
        var username = "testuser";

        _mockUserService.Setup(x => x.DeleteUserAsync(username))
            .ReturnsAsync(ServiceResult.Success("User deleted successfully"));

        // Act
        var result = await _accountManagementService.DeleteUserAsync(username);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("تم حذف المستخدم بنجاح");
        
        _mockUserService.Verify(x => x.DeleteUserAsync(username), Times.Once);
    }

    [Fact]
    public async Task ChangeUserPasswordAsync_ShouldValidateAndDelegateToUserService()
    {
        // Arrange
        var changePasswordDto = new AdminChangePasswordDto
        {
            Username = "testuser",
            NewPassword = "newpassword123",
            ConfirmPassword = "newpassword123"
        };

        _mockValidationService.Setup(x => x.ValidatePasswordConfirmation(
            changePasswordDto.NewPassword, changePasswordDto.ConfirmPassword))
            .Returns(ServiceResult.Success());
        
        _mockUserService.Setup(x => x.SetUserPasswordAsync(
            changePasswordDto.Username, changePasswordDto.NewPassword))
            .ReturnsAsync(ServiceResult.Success("تم تغيير كلمة المرور بنجاح"));

        // Act
        var result = await _accountManagementService.ChangeUserPasswordAsync(changePasswordDto);

        // Assert
        result.IsSuccess.Should().BeTrue();
        result.Message.Should().Be("تم تغيير كلمة المرور بنجاح");
        
        _mockValidationService.Verify(x => x.ValidatePasswordConfirmation(
            changePasswordDto.NewPassword, changePasswordDto.ConfirmPassword), Times.Once);
        _mockUserService.Verify(x => x.SetUserPasswordAsync(
            changePasswordDto.Username, changePasswordDto.NewPassword), Times.Once);
    }

    [Fact]
    public async Task ChangeUserPasswordAsync_WithMismatchedPasswords_ShouldFail()
    {
        // Arrange
        var changePasswordDto = new AdminChangePasswordDto
        {
            Username = "testuser",
            NewPassword = "newpassword123",
            ConfirmPassword = "differentpassword"
        };

        _mockValidationService.Setup(x => x.ValidatePasswordConfirmation(
            changePasswordDto.NewPassword, changePasswordDto.ConfirmPassword))
            .Returns(ServiceResult.Failure("كلمة المرور الجديدة وتأكيدها غير متطابقين"));

        // Act
        var result = await _accountManagementService.ChangeUserPasswordAsync(changePasswordDto);

        // Assert
        result.IsSuccess.Should().BeFalse();
        result.Message.Should().Be("كلمة المرور الجديدة وتأكيدها غير متطابقين");
        
        _mockUserService.Verify(x => x.SetUserPasswordAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
    }

    [Fact]
    public void IsValidEmail_ShouldDelegateToValidationService()
    {
        // Arrange
        var email = "<EMAIL>";
        _mockValidationService.Setup(x => x.ValidateEmail(email))
            .Returns(ServiceResult.Success());

        // Act
        var result = _accountManagementService.IsValidEmail(email);

        // Assert
        result.Should().BeTrue();
        _mockValidationService.Verify(x => x.ValidateEmail(email), Times.Once);
    }
}
