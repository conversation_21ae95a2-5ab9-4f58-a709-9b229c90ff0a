@model OrderFlowCore.Web.ViewModels.EmployeeViewModel

@{
    ViewData["Title"] = "إضافة موظف جديد";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="@Url.Action("Index", "Home")">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="@Url.Action("Index")">الموظفين</a></li>
                        <li class="breadcrumb-item active">إضافة موظف جديد</li>
                    </ol>
                </div>
                <h4 class="page-title">
                    <i class="fas fa-user-plus me-2"></i>إضافة موظف جديد
                </h4>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>بيانات الموظف الجديد
                    </h5>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Name" class="form-label">
                                        <i class="fas fa-user me-1"></i>اسم الموظف <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="Name" class="form-control" placeholder="أدخل اسم الموظف" />
                                    <span asp-validation-for="Name" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Job" class="form-label">
                                        <i class="fas fa-briefcase me-1"></i>الوظيفة <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Job" asp-items="Model.JobTypes" class="form-select">
                                        <option value="">اختر الوظيفة</option>
                                    </select>
                                    <span asp-validation-for="Job" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmployeeNumber" class="form-label">
                                        <i class="fas fa-id-badge me-1"></i>رقم الموظف <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="EmployeeNumber" class="form-control" placeholder="أدخل رقم الموظف" />
                                    <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="CivilNumber" class="form-label">
                                        <i class="fas fa-id-card me-1"></i>الرقم المدني <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="CivilNumber" class="form-control" placeholder="أدخل الرقم المدني" maxlength="12" />
                                    <span asp-validation-for="CivilNumber" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Nationality" class="form-label">
                                        <i class="fas fa-flag me-1"></i>الجنسية <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Nationality" asp-items="Model.Nationalities" class="form-select">
                                        <option value="">اختر الجنسية</option>
                                    </select>
                                    <span asp-validation-for="Nationality" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Mobile" class="form-label">
                                        <i class="fas fa-phone me-1"></i>رقم الهاتف <span class="text-danger">*</span>
                                    </label>
                                    <input asp-for="Mobile" class="form-control" placeholder="أدخل رقم الهاتف" maxlength="8" />
                                    <span asp-validation-for="Mobile" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="EmploymentType" class="form-label">
                                        <i class="fas fa-user-tie me-1"></i>نوع التوظيف <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="EmploymentType" asp-items="Model.EmploymentTypes" class="form-select">
                                        <option value="">اختر نوع التوظيف</option>
                                    </select>
                                    <span asp-validation-for="EmploymentType" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label asp-for="Qualification" class="form-label">
                                        <i class="fas fa-graduation-cap me-1"></i>المؤهل <span class="text-danger">*</span>
                                    </label>
                                    <select asp-for="Qualification" asp-items="Model.Qualifications" class="form-select">
                                        <option value="">اختر المؤهل</option>
                                    </select>
                                    <span asp-validation-for="Qualification" class="text-danger"></span>
                                </div>
                            </div>
                            
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>ملاحظة:</strong> جميع الحقول المميزة بـ <span class="text-danger">*</span> مطلوبة.
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a asp-action="Index" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-1"></i>العودة للقائمة
                                    </a>
                                    <div>
                                        <button type="reset" class="btn btn-outline-secondary me-2">
                                            <i class="fas fa-undo me-1"></i>إعادة تعيين
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save me-1"></i>حفظ الموظف
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        // Auto-format civil number input
        document.getElementById('CivilNumber').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 12) {
                value = value.substring(0, 12); // Limit to 12 digits
            }
            e.target.value = value;
        });

        // Auto-format mobile number input
        document.getElementById('Mobile').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 8) {
                value = value.substring(0, 8); // Limit to 8 digits
            }
            e.target.value = value;
        });

        // Form validation enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            const civilNumber = document.getElementById('CivilNumber').value;
            const name = document.getElementById('Name').value;

            if (!name.trim()) {
                e.preventDefault();
                alert('يرجى إدخال اسم الموظف');
                document.getElementById('Name').focus();
                return;
            }

            if (!civilNumber.trim()) {
                e.preventDefault();
                alert('يرجى إدخال الرقم المدني');
                document.getElementById('CivilNumber').focus();
                return;
            }

            if (civilNumber.length !== 12) {
                e.preventDefault();
                alert('الرقم المدني يجب أن يكون 12 رقم');
                document.getElementById('CivilNumber').focus();
                return;
            }
        });
    </script>
}
