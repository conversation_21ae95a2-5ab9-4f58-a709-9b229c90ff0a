using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Data
{
    public class JobTypeRepository : IJobTypeRepository
    {
        private readonly ApplicationDbContext _context;
        public JobTypeRepository(ApplicationDbContext context)
        {
            _context = context;
        }
        public async Task<IEnumerable<JobTypeDto>> GetAllAsync()
        {
            var jobTypes = await _context.JobTypes
                .AsNoTracking()
                .OrderBy(jt => jt.Name)
                .ToListAsync();

            return jobTypes.Select(MapToDto);
        }
        public async Task<JobTypeDto?> GetByIdAsync(int id)
        {
            var jobType = await _context.JobTypes
                .FirstOrDefaultAsync(jt => jt.Id == id);

            return jobType != null ? MapToDto(jobType) : null;
        }
        public async Task<bool> CreateAsync(JobTypeDto jobTypeDto)
        {
            var jobType = MapToEntity(jobTypeDto);
            await _context.JobTypes.AddAsync(jobType);
            
            return true;
        }
        public async Task<bool> UpdateAsync(JobTypeDto jobTypeDto)
        {
            var jobType = await _context.JobTypes.FindAsync(jobTypeDto.Id);
            if (jobType == null)
                return false;

            jobType.Name = jobTypeDto.Name;
            
            
            return true;
        }
        public async Task<bool> DeleteAsync(int id)
        {
            var jobType = await _context.JobTypes.FindAsync(id);
            if (jobType == null)
                return false;

            _context.JobTypes.Remove(jobType);
            
            return true;
        }
        public async Task<bool> ExistsAsync(int id)
        {
            return await _context.JobTypes.AnyAsync(jt => jt.Id == id);
        }
        private static JobTypeDto MapToDto(JobType jobType)
        {
            return new JobTypeDto
            {
                Id = jobType.Id,
                Name = jobType.Name,
                
            };
        }
        private static JobType MapToEntity(JobTypeDto jobTypeDto)
        {
            return new JobType
            {
                Id = jobTypeDto.Id,
                Name = jobTypeDto.Name,
            };
        }
    }
} 