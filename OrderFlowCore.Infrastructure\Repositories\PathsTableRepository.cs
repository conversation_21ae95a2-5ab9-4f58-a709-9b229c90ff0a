using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class PathsTableRepository : IPathsTableRepository
    {
        private readonly ApplicationDbContext _context;

        public PathsTableRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<string>> GetPathSupervisorsAsync(string pathColumn)
        {
            var supervisors = new List<string>();
            
            var query = pathColumn switch
            {
                "مسار1" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path1)).Select(p => p.Path1),
                "مسار2" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path2)).Select(p => p.Path2),
                "مسار3" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path3)).Select(p => p.Path3),
                "مسار4" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path4)).Select(p => p.Path4),
                "مسار5" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path5)).Select(p => p.Path5),
                "مسار6" => _context.PathsTables.Where(p => !string.IsNullOrEmpty(p.Path6)).Select(p => p.Path6),
                _ => null
            };

            if (query != null)
            {
                supervisors = await query.ToListAsync();
            }

            return supervisors.Where(s => !string.IsNullOrEmpty(s)).ToList();
        }

        public async Task<bool> CheckIfExistsInPathAsync(string text, string pathColumn)
        {
            var count = pathColumn switch
            {
                "مسار1" => await _context.PathsTables.CountAsync(p => p.Path1 == text),
                "مسار2" => await _context.PathsTables.CountAsync(p => p.Path2 == text),
                "مسار3" => await _context.PathsTables.CountAsync(p => p.Path3 == text),
                "مسار4" => await _context.PathsTables.CountAsync(p => p.Path4 == text),
                "مسار5" => await _context.PathsTables.CountAsync(p => p.Path5 == text),
                "مسار6" => await _context.PathsTables.CountAsync(p => p.Path6 == text),
                _ => 0
            };

            return count > 0;
        }

        public async Task AddToPathAsync(string text, string pathColumn)
        {
            // Check if text already exists
            if (await CheckIfExistsInPathAsync(text, pathColumn))
                return;

            // Find first row with NULL in the specified column
            var pathsTable = pathColumn switch
            {
                "مسار1" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path1 == null),
                "مسار2" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path2 == null),
                "مسار3" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path3 == null),
                "مسار4" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path4 == null),
                "مسار5" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path5 == null),
                "مسار6" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path6 == null),
                _ => null
            };

            if (pathsTable == null)
            {
                // Create new row if no empty slot found
                pathsTable = new PathsTable();
                _context.PathsTables.Add(pathsTable);
            }

            // Set the value in the appropriate column
            switch (pathColumn)
            {
                case "مسار1": pathsTable.Path1 = text; break;
                case "مسار2": pathsTable.Path2 = text; break;
                case "مسار3": pathsTable.Path3 = text; break;
                case "مسار4": pathsTable.Path4 = text; break;
                case "مسار5": pathsTable.Path5 = text; break;
                case "مسار6": pathsTable.Path6 = text; break;
            }

            await _context.SaveChangesAsync();
        }

        public async Task RemoveFromPathAsync(string text, string pathColumn)
        {
            var pathsTable = pathColumn switch
            {
                "مسار1" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path1 == text),
                "مسار2" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path2 == text),
                "مسار3" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path3 == text),
                "مسار4" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path4 == text),
                "مسار5" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path5 == text),
                "مسار6" => await _context.PathsTables.FirstOrDefaultAsync(p => p.Path6 == text),
                _ => null
            };

            if (pathsTable != null)
            {
                // Set the value to NULL instead of deleting the row
                switch (pathColumn)
                {
                    case "مسار1": pathsTable.Path1 = null; break;
                    case "مسار2": pathsTable.Path2 = null; break;
                    case "مسار3": pathsTable.Path3 = null; break;
                    case "مسار4": pathsTable.Path4 = null; break;
                    case "مسار5": pathsTable.Path5 = null; break;
                    case "مسار6": pathsTable.Path6 = null; break;
                }

                await _context.SaveChangesAsync();
            }
        }

        public async Task<Dictionary<string, List<string>>> GetAllPathsConfigurationAsync()
        {
            var pathsConfiguration = new Dictionary<string, List<string>>();

            pathsConfiguration["مسار1"] = await GetPathSupervisorsAsync("مسار1");
            pathsConfiguration["مسار2"] = await GetPathSupervisorsAsync("مسار2");
            pathsConfiguration["مسار3"] = await GetPathSupervisorsAsync("مسار3");
            pathsConfiguration["مسار4"] = await GetPathSupervisorsAsync("مسار4");
            pathsConfiguration["مسار5"] = await GetPathSupervisorsAsync("مسار5");
            pathsConfiguration["مسار6"] = await GetPathSupervisorsAsync("مسار6");

            return pathsConfiguration;
        }

        public async Task<List<PathsTable>> GetAllAsync()
        {
            return await _context.PathsTables.ToListAsync();
        }

        public async Task<PathsTable> GetByIdAsync(int id)
        {
            return await _context.PathsTables.FindAsync(id);
        }

        public async Task AddAsync(PathsTable entity)
        {
            _context.PathsTables.Add(entity);
            await _context.SaveChangesAsync();
        }

        public async Task UpdateAsync(PathsTable entity)
        {
            _context.PathsTables.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
            {
                _context.PathsTables.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }
    }
}
