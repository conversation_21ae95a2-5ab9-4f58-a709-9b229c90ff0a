using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class UserProfileViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المستخدم مطلوب")]
        [StringLength(50, ErrorMessage = "اسم المستخدم يجب أن لا يتجاوز 50 حرف")]
        [Display(Name = "اسم المستخدم")]
        public string Username { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string? Email { get; set; }

        [Phone(ErrorMessage = "رقم الهاتف غير صحيح")]
        [Display(Name = "رقم الهاتف")]
        public string? Phone { get; set; }

        [Display(Name = "الدور")]
        public string Role => string.IsNullOrEmpty(RoleType) ? UserRole.ToDisplayName(): RoleType;

        [Display(Name = "الدور")]
        public UserRole UserRole { get; set; } = UserRole.DirectManager;

        [Display(Name = "تخصص الدور")]
        public string? RoleType { get; set; }

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "آخر تحديث")]
        public DateTime? UpdatedAt { get; set; }

        public static UserProfileViewModel FromDto(UserProfileDto? data)
        {
            return new UserProfileViewModel
            {
                Id = data.Id,
                Username = data.Username,
                Email = data.Email,
                Phone = data.Phone,
                UserRole = data.UserRole,
                RoleType = data.RoleType,
                CreatedAt = data.CreatedAt,
                UpdatedAt = data.UpdatedAt
            };
        }

        public static UserProfileDto ToDto(UserProfileViewModel model)
        {
            return new UserProfileDto
            {
                Id = model.Id,
                Username = model.Username,
                Email = model.Email,
                Phone = model.Phone,
                UserRole = model.UserRole,
                RoleType = model.RoleType,
                CreatedAt = model.CreatedAt,
                UpdatedAt = model.UpdatedAt
            };
        }
    }
} 