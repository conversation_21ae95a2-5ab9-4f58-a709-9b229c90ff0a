using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs
{
    public class OrderNewDto
    {
        [Required]
        public string EmployeeName { get; set; }

        [Required]
        public string JobTitle { get; set; }

        [Required]
        public string EmployeeNumber { get; set; }

        [Required]
        public string CivilRecord { get; set; }

        [Required]
        public string Nationality { get; set; }

        [Required]
        public string MobileNumber { get; set; }

        [Required]
        public string Department { get; set; }

        [Required]
        public string EmploymentType { get; set; }

        [Required]
        public string Qualification { get; set; }

        [Required]
        public string OrderType { get; set; }

        public string Details { get; set; }

        public List<byte[]> Attachments { get; set; } = new();

        
    }
} 