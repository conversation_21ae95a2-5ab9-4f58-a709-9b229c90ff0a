using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRManagerViewModel
    {
        public HRManagerViewModel()
        {
            OrderNumbers = new List<SelectListItem>();
            StatusChangeOrderNumbers = new List<SelectListItem>();
            AvailableStatuses = new List<SelectListItem>();
        }

        // Main order processing section
        public List<SelectListItem> OrderNumbers { get; set; }
        public int SelectedOrderId { get; set; }

        [Display(Name = "سبب الإلغاء/الإعادة")]
        public string RejectReason { get; set; } = string.Empty;

        // Status change section
        public List<SelectListItem> StatusChangeOrderNumbers { get; set; }
        public int SelectedStatusChangeOrderId { get; set; }
        public List<SelectListItem> AvailableStatuses { get; set; }
        public string SelectedNewStatus { get; set; } = string.Empty;

        [Display(Name = "ملاحظات التغيير")]
        public string StatusChangeNotes { get; set; } = string.Empty;

        // Current order details for status change
        public string CurrentStatus { get; set; } = string.Empty;
        public string OrderType { get; set; } = string.Empty;

        // Settings
        public bool AutoDeleteAttachments { get; set; }

        // UI State
        public bool ShowStatusChangeSection { get; set; }
        public bool ShowOrderDetails { get; set; }
        public bool ShowStatusChangeDetails { get; set; }

        // Search and filter for status change
        public string SearchTerm { get; set; } = string.Empty;
        public string FilterPeriod { get; set; } = "today";
    }
}
