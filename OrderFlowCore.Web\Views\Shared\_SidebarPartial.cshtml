@using OrderFlowCore.Web.Extentions
@using OrderFlowCore.Web.Models
@using System.Security.Claims
@using OrderFlowCore.Web.Services
@inject IMenuService MenuService

@model string
@{
    var menuItems = MenuService.GetMenuItemsForUser(User);
    var settingsItems = MenuService.GetSettingsItemsForUser(User);
}

<aside class="app-sidebar bg-dark shadow" data-bs-theme="dark">
    @* Brand Logo *@
    <div class="sidebar-brand">
        <a href="@Url.Action("Index", "Dashboard")" class="brand-link">
            <div class="text-center">
                <img width="50" src="~/img/star.png" alt="Logo" />
                <div>مستشفى بريدة المركزي</div>
            </div>
        </a>
    </div>

    @* User Panel *@
    <div class="user-panel d-flex align-items-center border-bottom border-secondary p-3">
        <div class="image me-3">
            <i class="fas fa-user-circle fa-2x text-light"></i>
        </div>
        <div class="info">
            <a class="d-block text-light fw-bold" asp-controller="User" asp-action="Profile">
                @User.Identity!.Name
            </a>
            <small class="text-muted">@User.GetUserRoleToDisplay()</small>
        </div>
    </div>

    @* Sidebar Menu *@
    <nav class="mt-3">
        <ul class="nav nav-pills nav-sidebar flex-column" role="menu">
            @foreach (var item in menuItems)
            {
                <li class="nav-item">
                    <a href="@Url.Action("Index", item.Controller)"
                       class="nav-link text-light @(Model == item.Controller ? "active" : "")">
                        <i class="nav-icon @item.Icon me-2"></i>
                        <span>@item.Title</span>
                    </a>
                </li>
            }

            @* Settings Dropdown - Only show if there are settings items *@
            @if (settingsItems.Any())
            {
                <li class="nav-item">
                    <a href="#"
                       class="nav-link text-light @(settingsItems.Any(x => x.Controller == Model) ? "active" : "")"
                       data-bs-toggle="collapse"
                       data-bs-target="#settingsSubmenu"
                       aria-expanded="@(settingsItems.Any(x => x.Controller == Model) ? "true" : "false")">
                        <i class="nav-icon fas fa-cogs me-2"></i>
                        <span>
                            الإعدادات
                            <i class="fas fa-angle-down ms-2"></i>
                        </span>
                    </a>
                    <div class="collapse @(settingsItems.Any(x => x.Controller == Model) ? "show" : "")"
                         id="settingsSubmenu">
                        <ul class="nav nav-treeview ms-3">
                            @foreach (var item in settingsItems)
                            {
                                <li class="nav-item">
                                    <a href="@Url.Action("Index", item.Controller)"
                                       class="nav-link text-light @(Model == item.Controller ? "active" : "")">
                                        <i class="@item.Icon me-2"></i>
                                        <span>@item.Title</span>
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </li>
            }
        </ul>
    </nav>
</aside>
