using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;
using System;

namespace OrderFlowCore.Application.Services
{
    public class OrdersTypeService : IOrdersTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public OrdersTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<OrdersTypeDto>>> GetAllAsync()
        {
            try
            {
                var ordersTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                return ServiceResult<IEnumerable<OrdersTypeDto>>.Success(ordersTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<OrdersTypeDto>>.Failure($"Error retrieving orders types: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<OrdersTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var ordersType = await _unitOfWork.OrdersTypes.GetByIdAsync(id);
                if (ordersType == null)
                    return ServiceResult<OrdersTypeDto>.Failure("Orders type not found");
                    
                return ServiceResult<OrdersTypeDto>.Success(ordersType);
            }
            catch (Exception ex)
            {
                return ServiceResult<OrdersTypeDto>.Failure($"Error retrieving orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(OrdersTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Orders type created successfully");
                else
                    return ServiceResult.Failure("Failed to create orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(OrdersTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Orders type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating orders type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.OrdersTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                if (result)
                    return ServiceResult.Success("Orders type deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete orders type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting orders type: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
        {
            try
            {
                var ordersTypes = await _unitOfWork.OrdersTypes.GetAllAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("أنواع الطلبات");

                // Headers in Arabic
                worksheet.Cells[1, 1].Value = "الرقم";
                worksheet.Cells[1, 2].Value = "اسم نوع الطلب";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 2])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGreen);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Data rows
                int row = 2;
                foreach (var orderType in ordersTypes)
                {
                    worksheet.Cells[row, 1].Value = orderType.Id;
                    worksheet.Cells[row, 2].Value = orderType.Name;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var excelData = package.GetAsByteArray();
                return ServiceResult<byte[]>.Success(excelData, "تم تصدير أنواع الطلبات بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تصدير أنواع الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        var name = worksheet.Cells[row, 2].Value?.ToString()?.Trim(); // Skip ID column, use name from column 2

                        // Validate required fields
                        if (string.IsNullOrEmpty(name))
                        {
                            errors.Add($"الصف {row}: اسم نوع الطلب مطلوب");
                            continue;
                        }

                        // Check if order type already exists
                        var existingTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                        if (existingTypes.Any(ot => ot.Name.Equals(name, StringComparison.OrdinalIgnoreCase)))
                        {
                            errors.Add($"الصف {row}: نوع الطلب {name} موجود بالفعل");
                            continue;
                        }

                        var orderTypeDto = new OrdersTypeDto
                        {
                            Name = name
                        };

                        var createResult = await _unitOfWork.OrdersTypes.CreateAsync(orderTypeDto);
                        if (createResult)
                        {
                            importedCount++;
                        }
                        else
                        {
                            errors.Add($"الصف {row}: فشل في إضافة نوع الطلب {name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                var message = $"تم استيراد {importedCount} نوع طلب بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                var finalResult = ServiceResult<int>.Success(importedCount, message);
                finalResult.Errors = errors;
                return finalResult;
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في استيراد أنواع الطلبات: {ex.Message}");
            }
        }
    }
}