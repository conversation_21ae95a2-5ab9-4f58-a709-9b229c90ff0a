﻿using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Core.Models;

public partial class User
{
    public int Id { get; set; }
    [StringLength(100)]
    public string Username { get; set; }

    [StringLength(128)]
    public string Password { get; set; }

    public UserRole UserRole { get; set; } = UserRole.DirectManager;

    [StringLength(255)]
    public string? RoleType { get; set; }

    [StringLength(100)]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? Phone { get; set; }

    public bool IsActive { get; set; } = true;

    [StringLength(100)]
    public string? ResetToken { get; set; }

    public DateTime? ResetTokenExpiry { get; set; }
}