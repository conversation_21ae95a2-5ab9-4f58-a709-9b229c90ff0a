<a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button"
   data-bs-toggle="dropdown" data-bs-auto-close="true" aria-expanded="false">
    <i class="bi bi-person-circle"></i> الملف الشخصي
</a>
<div class="dropdown-menu dropdown-menu-start" aria-labelledby="navbarDropdown">
    <h6 class="dropdown-header">خيارات المستخدم</h6>
    <a class="dropdown-item" asp-controller="User" asp-action="Profile">
        <i class="fas fa-user-circle ms-2"></i> عرض الملف الشخصي
    </a>
    <a class="dropdown-item" asp-controller="User" asp-action="EditProfile">
        <i class="fas fa-edit ms-2"></i> تعديل الملف الشخصي
    </a>
    <a class="dropdown-item" asp-controller="User" asp-action="ChangePassword">
        <i class="fas fa-key ms-2"></i> تغيير كلمة المرور
    </a>
    <div class="dropdown-divider"></div>
    <form asp-controller="Auth" asp-action="Logout" method="post" class="dropdown-item">
        @Html.AntiForgeryToken()
        <button type="submit" class="btn btn-link text-danger p-0">
            <i class="fas fa-sign-out-alt ms-2"></i> تسجيل الخروج
        </button>
    </form>
</div>