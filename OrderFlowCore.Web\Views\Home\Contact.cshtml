@{
    ViewData["Title"] = "تواصل معنا";
    Layout = "_Layout";
}

<!-- Hero Section -->
<section class="hero-contact">
    <div class="container text-center text-white position-relative">
        <h1 class="display-3 fw-bold mb-4 animate-fade-in">تواصل معنا</h1>
        <p class="lead fs-4 mb-0 animate-fade-in" style="animation-delay: 0.2s;">نحن هنا لمساعدتك ونسعد بتواصلك معنا</p>
        
        <!-- Floating shapes -->
        <div class="floating-shape" style="top: 20%; right: 10%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;"></div>
        <div class="floating-shape" style="bottom: 30%; left: 5%; width: 80px; height: 80px; background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%); border-radius: 50%; animation-delay: -3s;"></div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-5" style="margin-top: -150px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="contact-form-card p-5">
                    <div class="text-center mb-5">
                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">أرسل رسالة</span>
                        <h2 class="display-6 fw-bold">كيف يمكننا مساعدتك؟</h2>
                        <p class="text-muted">املأ النموذج أدناه وسنتواصل معك في أقرب وقت</p>
                    </div>
                    
                    <form id="contactForm">
                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label-modern">الاسم الكامل <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-modern" required>
                                <div class="invalid-feedback">يرجى إدخال الاسم الكامل</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control form-control-modern" required>
                                <div class="invalid-feedback">يرجى إدخال بريد إلكتروني صحيح</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern">رقم الهاتف</label>
                                <input type="tel" class="form-control form-control-modern" placeholder="+966 5X XXX XXXX">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern">نوع الاستفسار</label>
                                <select class="form-select form-select-modern">
                                    <option selected>اختر نوع الاستفسار</option>
                                    <option value="1">استفسار عام</option>
                                    <option value="2">دعم فني</option>
                                    <option value="3">استفسار عن طلب</option>
                                    <option value="4">اقتراح أو تحسين</option>
                                    <option value="5">شكوى</option>
                                </select>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern">الموضوع <span class="text-danger">*</span></label>
                                <input type="text" class="form-control form-control-modern" required>
                                <div class="invalid-feedback">يرجى إدخال موضوع الرسالة</div>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern">الرسالة <span class="text-danger">*</span></label>
                                <textarea class="form-control form-control-modern" rows="5" required placeholder="اكتب رسالتك هنا..."></textarea>
                                <div class="invalid-feedback">يرجى كتابة الرسالة</div>
                            </div>
                            <div class="col-12 text-center mt-5">
                                <button type="submit" class="btn btn-send">
                                    <i class="fas fa-paper-plane me-2"></i>إرسال الرسالة
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Information Section -->
<section class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">معلومات التواصل</span>
            <h2 class="display-6 fw-bold">طرق التواصل المتاحة</h2>
            <p class="text-muted">يمكنك التواصل معنا عبر أي من الوسائل التالية</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card::before" style="background: radial-gradient(circle, #0891b2 0%, transparent 70%); opacity: 0.05;"></div>
                    <div class="contact-card-icon" style="background: rgba(8, 145, 178, 0.1);">
                        <i class="fas fa-map-marker-alt" style="color: var(--primary-color);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">العنوان</h5>
                    <p class="text-muted mb-1">مستشفى بريدة المركزي</p>
                    <p class="text-muted mb-1">القصيم، بريدة</p>
                    <p class="text-muted">المملكة العربية السعودية</p>
                    <a href="#map" class="btn btn-sm btn-outline-primary mt-3">
                        <i class="fas fa-directions me-1"></i>الاتجاهات
                    </a>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card::before" style="background: radial-gradient(circle, #10b981 0%, transparent 70%); opacity: 0.05;"></div>
                    <div class="contact-card-icon" style="background: rgba(16, 185, 129, 0.1);">
                        <i class="fas fa-phone" style="color: var(--success);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">أرقام الهاتف</h5>
                    <p class="text-muted mb-1" dir="ltr">+966 16 325 1111</p>
                    <p class="text-muted mb-1" dir="ltr">+966 16 325 2222</p>
                    <p class="text-muted">متاح 24/7</p>
                    <a href="tel:+966163251111" class="btn btn-sm btn-outline-success mt-3">
                        <i class="fas fa-phone me-1"></i>اتصل الآن
                    </a>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card::before" style="background: radial-gradient(circle, #f59e0b 0%, transparent 70%); opacity: 0.05;"></div>
                    <div class="contact-card-icon" style="background: rgba(245, 158, 11, 0.1);">
                        <i class="fas fa-envelope" style="color: var(--warning);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">البريد الإلكتروني</h5>
                    <p class="text-muted mb-1"><EMAIL></p>
                    <p class="text-muted mb-1"><EMAIL></p>
                    <p class="text-muted">نرد خلال 24 ساعة</p>
                    <a href="mailto:<EMAIL>" class="btn btn-sm btn-outline-warning mt-3">
                        <i class="fas fa-envelope me-1"></i>أرسل بريد
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Social Media Links -->
        <div class="social-links mt-5">
            <a href="#" class="social-link">
                <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-twitter"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-linkedin-in"></i>
            </a>
            <a href="#" class="social-link">
                <i class="fab fa-whatsapp"></i>
            </a>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">الأسئلة الشائعة</span>
            <h2 class="display-6 fw-bold">إجابات على أكثر الأسئلة شيوعاً</h2>
            <p class="text-muted">إذا لم تجد إجابة لسؤالك، لا تتردد في التواصل معنا</p>
        </div>
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="accordion accordion-modern" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#faq1">
                                <i class="fas fa-question-circle me-2"></i>
                                كيف يمكنني تقديم طلب جديد في النظام؟
                            </button>
                        </h2>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                يمكنك تقديم طلب جديد بسهولة من خلال الضغط على زر "طلب جديد" في القائمة الرئيسية أو من الصفحة الرئيسية. بعد ذلك، املأ النموذج بالمعلومات المطلوبة وأرفق المستندات اللازمة ثم اضغط على زر الإرسال.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq2">
                                <i class="fas fa-search me-2"></i>
                                كيف يمكنني متابعة حالة طلبي؟
                            </button>
                        </h2>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                لمتابعة حالة طلبك، انتقل إلى قسم "استعلام عن طلب" وأدخل رقم الطلب ورقم السجل المدني الخاص بك. ستظهر لك جميع تفاصيل الطلب والحالة الحالية ومراحل المعالجة.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq3">
                                <i class="fas fa-file-upload me-2"></i>
                                ما هي أنواع الملفات المقبولة وحجمها الأقصى؟
                            </button>
                        </h2>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                النظام يقبل ملفات PDF فقط للمستندات الرسمية. الحد الأقصى لحجم الملف الواحد هو 5 ميجابايت. يمكنك رفع عدة ملفات في الطلب الواحد بشرط عدم تجاوز الحجم الإجمالي 20 ميجابايت.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq4">
                                <i class="fas fa-clock me-2"></i>
                                كم يستغرق معالجة الطلب عادة؟
                            </button>
                        </h2>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                يختلف وقت معالجة الطلب حسب نوعه وطبيعته. عادة ما تتم معالجة الطلبات البسيطة خلال 2-3 أيام عمل، بينما قد تستغرق الطلبات المعقدة من 5-7 أيام عمل. ستتلقى إشعارات بكل تحديث على حالة طلبك.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#faq5">
                                <i class="fas fa-shield-alt me-2"></i>
                                هل معلوماتي الشخصية آمنة في النظام؟
                            </button>
                        </h2>
                        <div id="faq5" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                نعم، نحن نولي أمن المعلومات أولوية قصوى. جميع البيانات مشفرة باستخدام تقنيات التشفير المتقدمة SSL/TLS. كما نطبق أعلى معايير الأمان والخصوصية لحماية معلوماتك الشخصية.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5" id="map">
    <div class="container">
        <div class="text-center mb-5">
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">الموقع</span>
            <h2 class="display-6 fw-bold">موقعنا على الخريطة</h2>
        </div>
        <div class="map-container">
            <div class="map-placeholder">
                <i class="fas fa-map-marked-alt fa-4x text-muted mb-3"></i>
                <p class="text-muted">خريطة تفاعلية</p>
                <p class="text-muted small">مستشفى بريدة المركزي - القصيم</p>
            </div>
        </div>
    </div>
</section>

<!-- Back to Home -->
<section class="py-4 bg-light">
    <div class="container text-center">
        <a href="@Url.Action("Index", "Home")" class="btn btn-modern btn-primary-modern" style="background: var(--primary-color); color: white;">
            <i class="fas fa-arrow-right me-2"></i>العودة للصفحة الرئيسية
        </a>
    </div>
</section>

<!-- Success Message -->
<div class="success-message" id="successMessage">
    <i class="fas fa-check-circle fa-2x text-success"></i>
    <div>
        <h6 class="mb-0 fw-bold">تم إرسال رسالتك بنجاح!</h6>
        <small class="text-muted">سنتواصل معك في أقرب وقت ممكن</small>
    </div>
</div>

<!-- JavaScript -->
<script>
    // Form validation and submission
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Add validation classes
        if (!this.checkValidity()) {
            e.stopPropagation();
            this.classList.add('was-validated');
            return;
        }
        
        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        submitBtn.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
            // Reset form
            this.reset();
            this.classList.remove('was-validated');
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            
            // Show success message
            const successMsg = document.getElementById('successMessage');
            successMsg.classList.add('show');
            
            // Hide success message after 5 seconds
            setTimeout(() => {
                successMsg.classList.remove('show');
            }, 5000);
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }, 2000);
    });
    

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Phone number formatting
    const phoneInput = document.querySelector('input[type="tel"]');
    if (phoneInput) {
        phoneInput.addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '');
            let formattedValue = '';
            
            if (value.startsWith('+966')) {
                if (value.length > 4) formattedValue += value.substring(0, 4) + ' ';
                if (value.length > 5) formattedValue += value.substring(4, 6) + ' ';
                if (value.length > 6) formattedValue += value.substring(6, 9) + ' ';
                if (value.length > 9) formattedValue += value.substring(9, 13);
            } else {
                formattedValue = value;
            }
            
            e.target.value = formattedValue.trim();
        });
    }

    // FAQ accordion enhancement
    const accordionButtons = document.querySelectorAll('.accordion-button');
    accordionButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.className = 'ripple';
            this.appendChild(ripple);
            
            setTimeout(() => ripple.remove(), 600);
        });
    });

    // Social links hover effect
    const socialLinks = document.querySelectorAll('.social-link');
    socialLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.1) rotate(5deg)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1) rotate(0)';
        });
    });

    // Floating shapes parallax
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const shapes = document.querySelectorAll('.floating-shape');
        
        shapes.forEach((shape, index) => {
            const speed = 0.5 + (index * 0.2);
            shape.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
        });
    });

    // Contact cards interactive effect
    const contactCards = document.querySelectorAll('.contact-card');
    contactCards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 10;
            const rotateY = (centerX - x) / 10;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });
</script>
