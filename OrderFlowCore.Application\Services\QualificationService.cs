using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Services
{
    public class QualificationService : IQualificationService
    {
        private readonly IUnitOfWork _unitOfWork;
        public QualificationService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<QualificationDto>>> GetAllAsync()
        {
            try
            {
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();
                return ServiceResult<IEnumerable<QualificationDto>>.Success(qualifications);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<QualificationDto>>.Failure($"Error retrieving qualifications: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<QualificationDto>> GetByIdAsync(int id)
        {
            try
            {
                var qualification = await _unitOfWork.Qualifications.GetByIdAsync(id);
                if (qualification == null)
                    return ServiceResult<QualificationDto>.Failure("Qualification not found");
                    
                return ServiceResult<QualificationDto>.Success(qualification);
            }
            catch (Exception ex)
            {
                return ServiceResult<QualificationDto>.Failure($"Error retrieving qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(QualificationDto dto)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Qualification created successfully");
                else
                    return ServiceResult.Failure("Failed to create qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(QualificationDto dto)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Qualification updated successfully");
                else
                    return ServiceResult.Failure("Failed to update qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating qualification: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.Qualifications.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Qualification deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete qualification");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting qualification: {ex.Message}");
            }
        }
    }
} 