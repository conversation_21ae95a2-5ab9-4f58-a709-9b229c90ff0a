using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IPathManagementService
    {
        // Manual Routing (PathsTable) operations
        Task<ServiceResult<List<string>>> GetPathSupervisorsAsync(string pathColumn);
        Task<ServiceResult> UpdatePathSupervisorAsync(string pathColumn, string supervisorText, bool isChecked);
        Task<ServiceResult<Dictionary<string, List<string>>>> GetAllPathsConfigurationAsync();
        
        // Auto Routing operations
        Task<ServiceResult<List<AutoRouteDto>>> GetAutoRoutesAsync();
        Task<ServiceResult<AutoRouteDto>> GetAutoRouteByIdAsync(int id);
        Task<ServiceResult<AutoRouteDto>> CreateAutoRouteAsync(CreateAutoRouteDto dto, string userName);
        Task<ServiceResult<AutoRouteDto>> UpdateAutoRouteAsync(int id, UpdateAutoRouteDto dto, string userName);
        Task<ServiceResult> ToggleAutoRouteStatusAsync(int id);
        Task<ServiceResult> DeleteAutoRouteAsync(int id);
        Task<ServiceResult<bool>> CheckAutoRouteDuplicateAsync(string requestType, string nationality, string job, int? excludeId = null);
        Task<ServiceResult<int>> GetActiveAutoRoutesCountAsync();
        
        // Direct Routing operations
        Task<ServiceResult<List<DirectRouteDto>>> GetDirectRoutesAsync();
        Task<ServiceResult<DirectRouteDto>> GetDirectRouteByIdAsync(int id);
        Task<ServiceResult<DirectRouteDto>> CreateDirectRouteAsync(CreateDirectRouteDto dto, string userName);
        Task<ServiceResult<DirectRouteDto>> UpdateDirectRouteAsync(int id, UpdateDirectRouteDto dto, string userName);
        Task<ServiceResult> ToggleDirectRouteStatusAsync(int id);
        Task<ServiceResult> DeleteDirectRouteAsync(int id);
        Task<ServiceResult<bool>> CheckDirectRouteDuplicateAsync(string requestType, string nationality, string job, int? excludeId = null);
        Task<ServiceResult<int>> GetActiveDirectRoutesCountAsync();
        
        // Dropdown data for forms
        Task<ServiceResult<PathManagementDropdownDataDto>> GetDropdownDataAsync();
        
        // Supervisor management
        Task<ServiceResult<List<string>>> GetAvailableSupervisorsAsync();
        
        // Employee count update (for the UpdateEmployeeCount functionality)
        Task<ServiceResult> UpdateEmployeeCountAsync();

        Task<ServiceResult> UpdateAllManualPathsAsync(Dictionary<string, List<string>> paths);
    }
}
