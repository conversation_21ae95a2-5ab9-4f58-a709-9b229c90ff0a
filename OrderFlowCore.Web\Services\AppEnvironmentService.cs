﻿using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Web.Services
{
    public class AppEnvironmentService : IEnvironmentService
    {
        private readonly IWebHostEnvironment _env;

        public AppEnvironmentService(IWebHostEnvironment env)
        {
            _env = env;
        }

        public string EnvironmentName => _env.EnvironmentName;
        public string WebRootPath => _env.WebRootPath;
        public string ContentRootPath => _env.ContentRootPath;
    }
}

