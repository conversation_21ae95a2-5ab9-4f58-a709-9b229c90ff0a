using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IHRCoordinatorOrderService
{
    Task<ServiceResult<List<OrderSummaryDto>>> GetHRCoordinatorOrdersAsync();
    Task<ServiceResult<AutoRoutingInfoDto>> GetAutoRoutingInfoAsync(int orderId);
    Task<ServiceResult<DirectRoutingInfoDto>> GetDirectRoutingInfoAsync(int orderId);
    Task<ServiceResult> SubmitOrderByHrCoordinatorAsync(int orderId, string details, List<string> selectedSupervisors, string userName);
    Task<ServiceResult> AutoRouteOrderAsync(int orderId, string userName);
    Task<ServiceResult> RejectOrderByHRCoordinatorAsync(int orderId, string rejectReason, string userName);
    Task<ServiceResult> DirectOrderToManagerAsync(int orderId, string details, string userName);
    Task<ServiceResult> ReturnOrderToAssistantManagerAsync(int orderId, string reason, string? userName);
    Task<ServiceResult> OrderNeedsActionByCoordinatorAsync(int orderId, string actionDetails, string username);
    Task<ServiceResult> SubmitOrderToSupervisorsAsync(OrdersTable order, string details, List<string> selectedSupervisors, string coordinatorName, string orderTransferType);
}
