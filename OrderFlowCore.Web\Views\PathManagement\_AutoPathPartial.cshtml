@* Auto Routing Partial *@
@model OrderFlowCore.Web.ViewModels.PathManagementDashboardViewModel
<!-- Streamlined Form Section -->
<div class="form-section">
    <h4 class="mb-4" id="autoRouteFormTitle">إضافة مسار تلقائي جديد</h4>
    <form asp-action="SaveAutoRoute" method="post" class="auto-route-form">
        <div class="row g-3">
            <input type="hidden" id="autoRouteId" name="Id" />
            <div class="col-md-3">
                <label class="form-label">نوع الطلب</label>
                <select class="form-select" name="OrderType">
                    <option value="">اختر نوع الطلب</option>
                    @foreach (var item in Model.AutoOrderTypes)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الجنسية</label>
                <select class="form-select" name="Nationality">
                    <option value="">اختر الجنسية</option>
                    <option value="سعودي">سعودي</option>
                    <option value="غير سعودي">غير سعودي</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الوظيفة</label>
                <select class="form-select" name="Job">
                    <option value="">اختر الوظيفة</option>
                    @foreach (var item in Model.AutoJobTypes)
                    {
                        <option value="@item.Value">@item.Text</option>
                    }
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">الحالة</label>
                <div class="form-check form-switch mt-2">
                    <input class="form-check-input" type="checkbox" checked="" data-val="true" id="Status" name="Status" value="true">
                    <input name="Status" type="hidden" value="false">
                    <label class="form-check-label">نشط</label>
                </div>
            </div>
        </div>
        <div class="mt-4">
            <label class="form-label">اختر المشرفين</label>
            <div class="supervisor-grid">
                @foreach (var supervisor in Model.AutoSupervisors)
                {
                    <div class="supervisor-card">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" name="SelectedSupervisors" value="@supervisor">
                            <label class="form-check-label">@supervisor</label>
                        </div>
                    </div>
                }
            </div>
        </div>
        <div class="mt-4">
            <button type="submit" class="btn btn-primary-custom" id="autoRouteSubmitBtn">
                <i class="fas fa-save me-2"></i><span>حفظ المسار</span>
            </button>
            <button type="reset" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-undo me-2"></i>إعادة تعيين
            </button>
        </div>
    </form>
</div>
<!-- Routes Table -->
<div class="table-custom">
    <table class="table mb-0">
        <thead>
            <tr>
                <th>نوع الطلب</th>
                <th>الجنسية</th>
                <th>الوظيفة</th>
                <th>المشرفين</th>
                <th>الحالة</th>
                <th>الإجراءات</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var route in Model.AutoRoutes)
            {
                <tr>
                    <td>@route.OrderType</td>
                    <td>@route.Nationality</td>
                    <td>@route.Job</td>
                    <td><span class="badge bg-info">@route.SupervisorsCount مشرف</span></td>
                    <td><span class="status-badge @(route.Status ? "status-active" : "status-inactive")"><i class="fas @(route.Status ? "fa-check-circle" : "fa-times-circle")"></i> @route.StatusDisplay</span></td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1 btn-edit-auto" data-id="@route.Id">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger btn-delete-auto" data-id="@route.Id">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            }
        </tbody>
    </table>
</div> 