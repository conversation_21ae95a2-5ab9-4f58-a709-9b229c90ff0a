using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.ViewModels
{
    
    public class DistributionUpdateViewModel
    {
        [Required(ErrorMessage = "القسم مطلوب")]
        [Display(Name = "القسم")]
        public string DepartmentName { get; set; } = "";

        [Required(ErrorMessage = "مساعد المدير مطلوب")]
        [Display(Name = "مساعد المدير")]
        public AssistantManagerType AssistantManagerId { get; set; } = AssistantManagerType.Unknown;
    }

    public class BulkDistributionViewModel
    {
        [Required(ErrorMessage = "الأقسام مطلوبة")]
        [Display(Name = "الأقسام")]
        public List<string> SelectedDepartments { get; set; }

        [Required(ErrorMessage = "مساعد المدير مطلوب")]
        [Display(Name = "مساعد المدير")]
        public AssistantManagerType AssistantManagerId { get; set; } = AssistantManagerType.Unknown;
    }
} 