@model IEnumerable<OrderFlowCore.Application.DTOs.NationalityDto>
@{
    ViewData["Title"] = "إدارة الجنسيات";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة الجنسيات</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">الجنسيات</li>
                        </ol>
                    </nav>
                </div>
                <a asp-action="Create" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إضافة جنسية جديدة
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">قائمة الجنسيات</h5>
                </div>
                <div class="card-body">
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-flag fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد جنسيات</h5>
                            <p class="text-muted">قم بإضافة جنسية جديدة للبدء</p>
                            <a asp-action="Create" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>إضافة جنسية جديدة
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الجنسية</th>
                                        <th>الرمز</th>
                                        <th>الوصف</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var nationality in Model)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@nationality.Name</strong>
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(nationality.Code))
                                                {
                                                    <span class="badge bg-info">@nationality.Code</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(nationality.Description))
                                                {
                                                    <span class="text-muted">@nationality.Description</span>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لا يوجد وصف</span>
                                                }
                                            </td>
                                            <td>
                                                @if (nationality.IsActive)
                                                {
                                                    <span class="badge bg-success">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">غير نشط</span>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">@nationality.CreatedAt.ToString("dd/MM/yyyy")</small>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a asp-action="Edit" asp-route-id="@nationality.Id" 
                                                       class="btn btn-sm btn-outline-primary" 
                                                       title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@nationality.Id, '@nationality.Name')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف الجنسية: <strong id="nationalityName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function confirmDelete(id, name) {
            document.getElementById('nationalityName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

    </script>
} 