
:root {
    --primary-color: #0891b2;
    --primary-dark: #0e7490;
    --secondary-color: #06b6d4;
    --accent-color: #14b8a6;
    --dark-bg: #0f172a;
    --light-bg: #f8fafc;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
}


* {
    font-family: 'Tajawal', sans-serif;
}

body {
    background-color: var(--light-bg);
    color: var(--text-dark);
    overflow-x: hidden;
}

.text-primary{
    color: var(--primary-color) !important;
}


/* Modern Navbar */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

    .navbar-modern.scrolled {
        background: rgba(255, 255, 255, 0.98);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

.nav-link {
    color: var(--text-dark) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem !important;
}

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 50%;
        width: 0;
        height: 2px;
        background: var(--primary-color);
        transition: all 0.3s ease;
        transform: translateX(50%);
    }

    .nav-link:hover::after {
        width: 80%;
    }

/* Hero Section */
.hero-modern {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
    min-height: 90vh;
    display: flex;
    align-items: center;
}

    .hero-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="0.1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat bottom;
        background-size: cover;
    }

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    animation: fadeInUp 0.8s ease;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 300;
    margin-bottom: 3rem;
    opacity: 0.95;
    animation: fadeInUp 0.8s ease 0.2s both;
}

/* Modern Buttons */
.btn-modern {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    display: inline-block;
    margin: 0.5rem;
}


    .btn-modern::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .btn-modern:hover::before {
        width: 300px;
        height: 300px;
    }

    .btn-modern:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

.btn-white-modern {
    background: white;
    color: var(--primary-color);
}

.btn-secondary-modern {
    background: transparent;
    color: white;
    border: 2px solid white;
}

    .btn-secondary-modern:hover {
        background: white;
        color: var(--primary-color);
    }



.btn-primary-modern {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
}
/* Stats Cards */
.stat-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

    .stat-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: linear-gradient(45deg, transparent, rgba(var(--primary-rgb), 0.05), transparent);
        transform: rotate(45deg);
        transition: all 0.5s ease;
    }

    .stat-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

        .stat-card:hover::before {
            animation: shine 0.5s ease;
        }

.stat-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    font-size: 2rem;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

/* Process Steps */
.process-step {
    text-align: center;
    position: relative;
    padding: 2rem;
}

    .process-step::after {
        content: '';
        position: absolute;
        top: 40px;
        left: -50%;
        width: 100%;
        height: 2px;
        background: linear-gradient(to left, var(--primary-color), transparent);
        z-index: -1;
    }

    .process-step:first-child::after {
        display: none;
    }

.step-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    font-weight: bold;
    position: relative;
    box-shadow: 0 10px 30px rgba(8, 145, 178, 0.3);
    transition: all 0.3s ease;
}

.process-step:hover .step-icon {
    transform: scale(1.1);
    box-shadow: 0 15px 40px rgba(8, 145, 178, 0.4);
}

/* Feature Cards */
.feature-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        opacity: 0.1;
        border-radius: 50%;
        transform: translate(30px, -30px);
        transition: all 0.3s ease;
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    }

        .feature-card:hover::before {
            transform: translate(20px, -20px) scale(1.2);
        }

.feature-icon {
    font-size: 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1.5rem;
}

/* Search Section */
.search-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.search-card {
    background: white;
    border-radius: 30px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: relative;
}

    .search-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 300px;
        height: 300px;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        opacity: 0.05;
        border-radius: 50%;
        transform: translate(100px, -100px);
    }

/* Form Elements */
.form-label-modern-primary {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

    .form-label-modern-primary.small {
        font-size: 0.875rem;
        font-weight: 500;
    }

.form-label-modern-dark {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}
.form-control-modern {
    border: 1px solid #e2e8f0;
    border-radius: 15px;
    padding: 1rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

    .form-control-modern:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    .form-control-modern:hover {
        border-color: var(--primary-color);
    }

.form-control-modern, .form-select-modern {
    border: 2px solid #e2e8f0;
    border-radius: 15px;
    padding: 1rem 1.5rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: #f8fafc;
}

    .form-control-modern:focus, .form-select-modern:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(8, 145, 178, 0.1);
        background-color: white;
    }

.floating {
    animation: float 6s ease-in-out infinite;
}

.form-text {
    color: var(--text-secondary);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

    .form-text.small {
        font-size: 0.7rem;
    }

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    border-radius: var(--radius-sm);
}


/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    position: relative;
    overflow: hidden;
}

    .cta-section::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 20s ease-in-out infinite;
    }


/* Footer */
.footer-modern {
    background: var(--dark-bg);
    color: white;
    position: relative;
    overflow: hidden;
}

    .footer-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(to right, transparent, var(--primary-color), transparent);
    }

.footer-link {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

    .footer-link:hover {
        color: var(--primary-color);
        transform: translateX(-5px);
    }

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 5px;
}

    ::-webkit-scrollbar-thumb:hover {
        background: var(--primary-dark);
    }


.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: scale(0);
    animation: ripple-animation 0.6s ease-out;
}


.is-invalid {
    border-color: var(--danger) !important;
}

body {
    opacity: 0;
    transition: opacity 0.5s ease;
}

    body.loaded {
        opacity: 1;
    }


/* Section Separators */
.section-separator {
    position: relative;
    padding: 5rem 0;
}

    .section-separator::before {
        content: '';
        position: absolute;
        top: 0;
        left: 10%;
        right: 10%;
        height: 1px;
        background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    }


/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Modern Buttons */
.btn-modern-about {
    padding: 1rem 2.5rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

    .btn-modern-about:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

/* Progress Bars */
.progress-modern {
    height: 10px;
    border-radius: 10px;
    background: #e2e8f0;
    overflow: visible;
}

.progress-bar-modern {
    border-radius: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    position: relative;
    box-shadow: 0 2px 4px rgba(8, 145, 178, 0.2);
}

    .progress-bar-modern::after {
        content: attr(data-percentage);
        position: absolute;
        right: -40px;
        top: -25px;
        font-weight: bold;
        color: var(--primary-color);
    }


/* Form validation styles */
.was-validated .form-control:invalid,
.was-validated .form-select:invalid {
    border-color: var(--danger);
    background-image: none;
}

.was-validated .form-control:valid,
.was-validated .form-select:valid {
    border-color: var(--success);
    background-image: none;
}

/* Loading state */
.btn-send:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Perspective for 3D effects */
.contact-card {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
}

/* Map hover effect */
.map-container:hover .map-placeholder {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}


:root {
    --primary-color: #0891b2;
    --primary-dark: #0e7490;
    --secondary-color: #06b6d4;
    --accent-color: #14b8a6;
    --dark-bg: #0f172a;
    --light-bg: #f8fafc;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
}

/* Hero Section */
.hero-contact {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 150px;
}

    .hero-contact::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 120px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,80C672,64,768,64,864,80C960,96,1056,128,1152,138.7C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat;
        background-size: cover;
    }

    .hero-contact::after {
        content: '';
        position: absolute;
        top: 10%;
        left: -150px;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        border-radius: 50%;
    }

/* Contact Form Card */
.contact-form-card {
    background: white;
    border-radius: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transform: translateY(-100px);
    margin-bottom: -50px;
}

    .contact-form-card::before {
        content: '';
        position: absolute;
        top: -150px;
        right: -150px;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
        opacity: 0.05;
    }


/* Contact Info Cards */
.contact-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}

    .contact-card::before {
        content: '';
        position: absolute;
        top: -100px;
        right: -100px;
        width: 200px;
        height: 200px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .contact-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    }

.contact-card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 20px;
    font-size: 2rem;
    position: relative;
    z-index: 1;
}

/* FAQ Accordion */
.accordion-modern {
    border: none;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
}

    .accordion-modern .accordion-item {
        background: white;
        border: none;
        margin-bottom: 1rem;
        border-radius: 15px !important;
        overflow: hidden;
        transition: all 0.3s ease;
    }

        .accordion-modern .accordion-item:hover {
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
        }

    .accordion-modern .accordion-button {
        background: white;
        color: var(--text-dark);
        font-weight: 600;
        padding: 1.5rem;
        border: none;
        border-radius: 15px !important;
        position: relative;
        transition: all 0.3s ease;
    }

        .accordion-modern .accordion-button:not(.collapsed) {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            box-shadow: none;
        }

        .accordion-modern .accordion-button::after {
            background-image: none;
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            transition: all 0.3s ease;
        }

        .accordion-modern .accordion-button:not(.collapsed)::after {
            transform: rotate(180deg);
            color: white;
        }

    .accordion-modern .accordion-body {
        padding: 1.5rem;
        color: var(--text-light);
    }

/* Modern Button */
.btn-send {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1rem 3rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    border: none;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(8, 145, 178, 0.3);
}

    .btn-send:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(8, 145, 178, 0.4);
        color: white;
    }

    .btn-send::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        transform: translate(-50%, -50%);
        transition: width 0.6s, height 0.6s;
    }

    .btn-send:hover::before {
        width: 300px;
        height: 300px;
    }

/* Floating Elements */
.floating-shape {
    position: absolute;
    opacity: 0.1;
    animation: float 6s ease-in-out infinite;
}


/* Map Section */
.map-container {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    height: 400px;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.map-placeholder {
    text-align: center;
    color: var(--text-light);
}

/* Social Links */
.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.social-link {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

    .social-link:hover {
        transform: translateY(-5px) scale(1.1);
        box-shadow: 0 10px 20px rgba(8, 145, 178, 0.3);
        color: white;
    }

.animate-fade-in {
    animation: fadeInUp 0.8s ease forwards;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 100px;
    right: 20px;
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: none;
    align-items: center;
    gap: 1rem;
    z-index: 1000;
}

    .success-message.show {
        display: flex;
        animation: slideIn 0.5s ease;
    }

:root {
    --primary-color: #0891b2;
    --primary-dark: #0e7490;
    --secondary-color: #06b6d4;
    --accent-color: #14b8a6;
    --dark-bg: #0f172a;
    --light-bg: #f8fafc;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
}

/* Hero Section */
.hero-instructions {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 80px;
}

    .hero-instructions::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 100px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23f8fafc" fill-opacity="1" d="M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat;
        background-size: cover;
    }

/* Sidebar Navigation */
.sidebar-nav {
    background: white;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    position: sticky;
    top: 100px;
}

.sidebar-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.sidebar-nav .nav-link {
    color: var(--text-dark);
    padding: 1rem 1.5rem;
    border: none;
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
}

    .sidebar-nav .nav-link::before {
        content: '';
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 0;
        background: var(--primary-color);
        transition: height 0.3s ease;
    }

    .sidebar-nav .nav-link:hover,
    .sidebar-nav .nav-link.active {
        background: rgba(8, 145, 178, 0.05);
        color: var(--primary-color);
        padding-right: 2rem;
    }

        .sidebar-nav .nav-link:hover::before,
        .sidebar-nav .nav-link.active::before {
            height: 100%;
        }

/* Content Cards */
.instruction-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

    .instruction-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
    }

.card-header-modern {
    padding: 1.5rem;
    border: none;
    position: relative;
    overflow: hidden;
}

    .card-header-modern::after {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    }

    .card-header-modern h3 {
        margin: 0;
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .card-header-modern i {
        font-size: 1.5rem;
    }

/* Status badges with modern style */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-weight: 600;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

    .status-badge:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    .status-badge.bg-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
        color: white;
    }
/* Step Cards */
.step-card {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 15px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

    .step-card::before {
        content: '';
        position: absolute;
        top: -100px;
        right: -100px;
        width: 200px;
        height: 200px;
        background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
        opacity: 0.05;
        transition: all 0.3s ease;
    }

    .step-card:hover {
        border-color: var(--primary-color);
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(8, 145, 178, 0.15);
    }

        .step-card:hover::before {
            opacity: 0.1;
            transform: scale(1.2);
        }

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-bottom: 1rem;
    box-shadow: 0 4px 10px rgba(8, 145, 178, 0.3);
}

/* Modern Alert Boxes */
.alert-modern {
    border: none;
    border-radius: 15px;
    padding: 1.25rem 1.5rem;
    position: relative;
    overflow: hidden;
}

    .alert-modern::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4px;
    }

    .alert-modern.alert-info {
        background: rgba(59, 130, 246, 0.1);
        color: #1e40af;
    }

        .alert-modern.alert-info::before {
            background: #3b82f6;
        }

    .alert-modern.alert-warning {
        background: rgba(245, 158, 11, 0.1);
        color: #92400e;
    }

        .alert-modern.alert-warning::before {
            background: #f59e0b;
        }

    .alert-modern.alert-danger {
        background: rgba(239, 68, 68, 0.1);
        color: #991b1b;
    }

        .alert-modern.alert-danger::before {
            background: #ef4444;
        }

/* Modern Table */
.table-modern {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

    .table-modern thead {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
    }

    .table-modern th {
        border: none;
        padding: 1rem 1.5rem;
        font-weight: 600;
    }

    .table-modern td {
        padding: 1rem 1.5rem;
        border-color: #f1f5f9;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

        .table-modern tbody tr:hover {
            background: rgba(8, 145, 178, 0.05);
            transform: scale(1.01);
        }
/* Base Timeline Styling */
.timeline {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 2.5rem;
    padding: 3rem 1rem;
    max-width: 800px;
    margin: auto;
}

    /* Line in the middle */
    .timeline::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        width: 4px;
        height: 100%;
        background: linear-gradient(to bottom, var(--primary-color), var(--accent-color));
        transform: translateX(-50%);
        z-index: 0;
    }

/* Each item */
.timeline-item {
    position: relative;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.timeline-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 1rem 2rem;
    background: var(--light-bg);
    border-radius: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    width: 100%;
    z-index: 1;
    transition: all 0.3s ease;
}

.timeline-item:nth-child(even) .timeline-content {
    flex-direction: row-reverse;
    text-align: right;
}

.timeline-icon {
    min-width: 60px;
    min-height: 60px;
    background: white;
    border: 4px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.5rem;
    color: var(--primary-color);
    transition: 0.3s ease;
}

.timeline-content:hover .timeline-icon {
    background: var(--primary-color);
    color: white;
    transform: scale(1.15);
}

.timeline-text h6 {
    font-size: 1.1rem;
    font-weight: 700;
    margin-bottom: 0.3rem;
    color: var(--dark-bg);
}

.timeline-text p {
    margin: 0;
    color: #6b7280;
    font-size: 0.95rem;
}


/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    z-index: 1000;
}

.scroll-progress {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    width: 0%;
    transition: width 0.1s ease;
}


/*Order Details*/

:root {
    --primary-color: #0891b2;
    --primary-dark: #0e7490;
    --secondary-color: #06b6d4;
    --accent-color: #14b8a6;
    --dark-bg: #0f172a;
    --light-bg: #f8fafc;
    --text-dark: #1e293b;
    --text-light: #64748b;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
}

/* Hero Section */
.hero-details {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    position: relative;
    overflow: hidden;
    padding: 120px 0 180px;
}

    .hero-details::before {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 120px;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320"><path fill="%23ffffff" fill-opacity="1" d="M0,64L48,80C96,96,192,128,288,128C384,128,480,96,576,80C672,64,768,64,864,80C960,96,1056,128,1152,138.7C1248,149,1344,139,1392,133.3L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path></svg>') no-repeat;
        background-size: cover;
    }

/* Progress Card */
.progress-card {
    background: white;
    border-radius: 30px;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    transform: translateY(-120px);
    margin-bottom: -60px;
    padding: 3rem;
}

    .progress-card::before {
        content: '';
        position: absolute;
        top: -100px;
        right: -100px;
        width: 300px;
        height: 300px;
        background: radial-gradient(circle, var(--primary-color) 0%, transparent 70%);
        opacity: 0.05;
    }

/* Modern Progress Steps */
.progress-steps {
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.progress-step {
    flex: 1;
    text-align: center;
    position: relative;
    min-width: 120px;
    margin: 0.5rem;
}

    .progress-step::after {
        content: '';
        position: absolute;
        top: 25px;
        left: 50%;
        width: 100%;
        height: 3px;
        background: #e2e8f0;
        z-index: -1;
    }

    .progress-step:last-child::after {
        display: none;
    }

.step-circle {
    width: 50px;
    height: 50px;
    background: #e2e8f0;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: var(--text-light);
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-step.completed .step-circle {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    animation: scaleIn 0.3s ease;
}

.progress-step.active .step-circle {
    background: white;
    border: 3px solid var(--primary-color);
    color: var(--primary-color);
    box-shadow: 0 0 0 8px rgba(8, 145, 178, 0.1);
    animation: pulse 2s infinite;
}

.progress-step.needs-action .step-circle {
    background: var(--warning);
    color: white;
    animation: shake 0.5s ease;
}

.progress-step.cancelled .step-circle {
    background: var(--danger);
    color: white;
}

.step-label {
    font-size: 0.875rem;
    color: var(--text-light);
    font-weight: 500;
}

.progress-step.completed .step-label,
.progress-step.active .step-label {
    color: var(--text-dark);
    font-weight: 600;
}

/* Modern Progress Bar */
.progress-bar-modern {
    background: #e2e8f0;
    height: 10px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    border-radius: 10px;
    transition: width 1s ease;
    position: relative;
    box-shadow: 0 2px 4px rgba(8, 145, 178, 0.2);
}

    .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        animation: shimmer 2s infinite;
    }

/* Details Tables */
.details-section {
    background: white;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    margin-bottom: 2rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

    .details-section:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.12);
    }

.section-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 1rem;
}

    .section-header i {
        font-size: 1.5rem;
    }

.details-table {
    width: 100%;
    border-collapse: collapse;
}

    .details-table th {
        background: #f8fafc;
        padding: 1rem 1.5rem;
        text-align: right;
        font-weight: 600;
        color: var(--text-dark);
        border-bottom: 2px solid #e2e8f0;
        white-space: nowrap;
    }

    .details-table td {
        padding: 1rem 1.5rem;
        border-bottom: 1px solid #f1f5f9;
        color: var(--text-light);
    }

    .details-table tr:hover {
        background: rgba(8, 145, 178, 0.02);
    }

    .details-table tr:last-child td {
        border-bottom: none;
    }


/* File Upload Section */
.file-upload-section {
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-top: 2rem;
}

.file-upload-item {
    background: white;
    border: 2px dashed #e2e8f0;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

    .file-upload-item:hover {
        border-color: var(--primary-color);
        background: rgba(8, 145, 178, 0.02);
    }

.upload-btn {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-weight: 600;
    transition: all 0.3s ease;
}

    .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(8, 145, 178, 0.3);
    }

/* Uploaded Files Display */
.uploaded-file {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

    .uploaded-file:hover {
        border-color: var(--primary-color);
        transform: translateX(-5px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    }

    .uploaded-file a {
        color: var(--text-dark);
        text-decoration: none;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

        .uploaded-file a:hover {
            color: var(--primary-color);
        }



/* Alert Modern */
.alert-modern {
    border: none;
    border-radius: 15px;
    padding: 1.25rem 1.5rem;
    position: relative;
    overflow: hidden;
}

    .alert-modern.alert-info {
        background: rgba(59, 130, 246, 0.1);
        color: #1e40af;
        border-left: 4px solid #3b82f6;
    }




/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes shine {
    from {
        transform: rotate(45deg) translateY(0);
    }

    to {
        transform: rotate(45deg) translateY(200%);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(8, 145, 178, 0.4);
    }

    70% {
        box-shadow: 0 0 0 20px rgba(8, 145, 178, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(8, 145, 178, 0);
    }
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
@keyframes slideIn {
    from {
        transform: translateX(100%);
    }

    to {
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        transform: scale(0);
    }

    to {
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }

    25% {
        transform: translateX(-5px);
    }

    75% {
        transform: translateX(5px);
    }
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}


/* Responsive */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.2rem;
    }

    .process-step::after {
        display: none;
    }

    .hero-about {
        padding: 80px 0 60px;
    }

    .stats-card {
        transform: translateY(0);
        margin-top: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .hero-contact {
        padding: 80px 0 120px;
    }

    .contact-form-card {
        transform: translateY(-50px);
        border-radius: 20px;
    }

    .contact-card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .sidebar-nav {
        position: static;
        margin-bottom: 2rem;
    }

    .hero-instructions {
        padding: 80px 0 60px;
    }

    .timeline::before {
        left: 20px;
    }

    .timeline-item {
        text-align: right;
        padding-right: 60px;
    }

    .timeline-icon {
        position: absolute;
        right: 0;
    }

    .timeline-content {
        flex-direction: column;
        text-align: center;
    }

    .timeline-item:nth-child(even) .timeline-content {
        flex-direction: column;
    }

    .progress-steps {
        flex-direction: column;
    }

    .progress-step {
        margin: 1rem 0;
    }

        .progress-step::after {
            display: none;
        }

    .details-table {
        font-size: 0.875rem;
    }

        .details-table th,
        .details-table td {
            padding: 0.75rem;
        }
}