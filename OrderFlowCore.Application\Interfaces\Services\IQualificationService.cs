using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;
using System.IO;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IQualificationService
{
    Task<ServiceResult<IEnumerable<QualificationDto>>> GetAllAsync();
    Task<ServiceResult<QualificationDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(QualificationDto dto);
    Task<ServiceResult> UpdateAsync(QualificationDto dto);
    Task<ServiceResult> DeleteAsync(int id);

    // Import/Export functionality
    Task<ServiceResult<byte[]>> ExportToExcelAsync();
    Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream);
}
