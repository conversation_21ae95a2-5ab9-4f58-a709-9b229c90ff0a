<svg width="500" height="500" viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0891b2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#06b6d4;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#14b8a6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#10b981;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="screenGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f0f9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e0f2fe;stop-opacity:1" />
    </linearGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3"/>
      <feOffset dx="0" dy="4" result="offsetblur"/>
      <feFlood flood-color="#000000" flood-opacity="0.1"/>
      <feComposite in2="offsetblur" operator="in"/>
      <feMerge>
        <feMergeNode/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background circle decoration -->
  <circle cx="400" cy="100" r="80" fill="url(#primaryGradient)" opacity="0.1"/>
  <circle cx="100" cy="400" r="60" fill="url(#accentGradient)" opacity="0.1"/>
  
  <!-- Main Dashboard/Tablet -->
  <g transform="translate(250, 200)">
    <!-- Tablet body -->
    <rect x="-120" y="-80" width="240" height="160" rx="10" fill="#1e293b" filter="url(#shadow)"/>
    
    <!-- Screen -->
    <rect x="-110" y="-70" width="220" height="140" rx="5" fill="url(#screenGradient)"/>
    
    <!-- Dashboard elements -->
    <!-- Header -->
    <rect x="-100" y="-60" width="200" height="30" rx="5" fill="url(#primaryGradient)"/>
    <text x="0" y="-40" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">نظام إدارة الطلبات</text>
    
    <!-- Stats cards -->
    <g transform="translate(-90, -20)">
      <!-- Card 1 -->
      <rect x="0" y="0" width="55" height="40" rx="5" fill="white" filter="url(#shadow)"/>
      <circle cx="15" cy="15" r="8" fill="url(#primaryGradient)" opacity="0.2"/>
      <text x="15" y="18" text-anchor="middle" fill="#0891b2" font-size="10" font-weight="bold">150</text>
      <text x="27" y="32" text-anchor="middle" fill="#64748b" font-size="8">طلبات جديدة</text>
      
      <!-- Card 2 -->
      <rect x="65" y="0" width="55" height="40" rx="5" fill="white" filter="url(#shadow)"/>
      <circle cx="80" cy="15" r="8" fill="url(#accentGradient)" opacity="0.2"/>
      <text x="80" y="18" text-anchor="middle" fill="#14b8a6" font-size="10" font-weight="bold">89</text>
      <text x="92" y="32" text-anchor="middle" fill="#64748b" font-size="8">قيد المراجعة</text>
      
      <!-- Card 3 -->
      <rect x="130" y="0" width="55" height="40" rx="5" fill="white" filter="url(#shadow)"/>
      <circle cx="145" cy="15" r="8" fill="#f59e0b" opacity="0.2"/>
      <text x="145" y="18" text-anchor="middle" fill="#f59e0b" font-size="10" font-weight="bold">234</text>
      <text x="157" y="32" text-anchor="middle" fill="#64748b" font-size="8">مكتملة</text>
    </g>
    
    <!-- Chart area -->
    <rect x="-90" y="30" width="180" height="30" rx="5" fill="white" filter="url(#shadow)"/>
    <!-- Simple bar chart -->
    <rect x="-80" y="40" width="15" height="10" fill="url(#primaryGradient)"/>
    <rect x="-60" y="37" width="15" height="13" fill="url(#primaryGradient)"/>
    <rect x="-40" y="35" width="15" height="15" fill="url(#primaryGradient)"/>
    <rect x="-20" y="38" width="15" height="12" fill="url(#primaryGradient)"/>
    <rect x="0" y="36" width="15" height="14" fill="url(#primaryGradient)"/>
    <rect x="20" y="39" width="15" height="11" fill="url(#primaryGradient)"/>
    <rect x="40" y="35" width="15" height="15" fill="url(#primaryGradient)"/>
    <rect x="60" y="40" width="15" height="10" fill="url(#primaryGradient)"/>
  </g>
  
  <!-- Floating document -->
  <g transform="translate(120, 150) rotate(-10)">
    <rect x="-30" y="-40" width="60" height="80" rx="5" fill="white" filter="url(#shadow)"/>
    <rect x="-20" y="-30" width="40" height="4" rx="2" fill="#e2e8f0"/>
    <rect x="-20" y="-20" width="30" height="4" rx="2" fill="#e2e8f0"/>
    <rect x="-20" y="-10" width="35" height="4" rx="2" fill="#e2e8f0"/>
    <rect x="-20" y="0" width="25" height="4" rx="2" fill="#e2e8f0"/>
    <circle cx="0" cy="20" r="10" fill="url(#accentGradient)"/>
    <path d="M -5 20 L -2 23 L 5 16" stroke="white" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  
  <!-- Medical cross symbol -->
  <g transform="translate(380, 300)">
    <circle cx="0" cy="0" r="25" fill="white" filter="url(#shadow)"/>
    <rect x="-3" y="-12" width="6" height="24" rx="2" fill="url(#primaryGradient)"/>
    <rect x="-12" y="-3" width="24" height="6" rx="2" fill="url(#primaryGradient)"/>
  </g>
  
  <!-- Floating notification -->
  <g transform="translate(350, 150)">
    <rect x="-40" y="-15" width="80" height="30" rx="15" fill="url(#accentGradient)" filter="url(#shadow)"/>
    <circle cx="-20" cy="0" r="8" fill="white" opacity="0.3"/>
    <text x="5" y="4" text-anchor="middle" fill="white" font-size="10" font-weight="bold">تم الموافقة</text>
  </g>
  
  <!-- User icon -->
  <g transform="translate(100, 250)">
    <circle cx="0" cy="0" r="30" fill="#f0f9ff" filter="url(#shadow)"/>
    <circle cx="0" cy="-5" r="10" fill="url(#primaryGradient)"/>
    <path d="M -15 15 Q -15 5 -10 0 L 10 0 Q 15 5 15 15" fill="url(#primaryGradient)"/>
  </g>
  
  <!-- Clock/Time icon -->
  <g transform="translate(400, 400)">
    <circle cx="0" cy="0" r="25" fill="white" filter="url(#shadow)"/>
    <circle cx="0" cy="0" r="20" fill="none" stroke="url(#primaryGradient)" stroke-width="2"/>
    <line x1="0" y1="0" x2="0" y2="-10" stroke="#0891b2" stroke-width="2" stroke-linecap="round"/>
    <line x1="0" y1="0" x2="7" y2="5" stroke="#0891b2" stroke-width="2" stroke-linecap="round"/>
    <circle cx="0" cy="0" r="2" fill="#0891b2"/>
  </g>
  
  <!-- Decorative dots -->
  <circle cx="150" cy="350" r="3" fill="#0891b2" opacity="0.3"/>
  <circle cx="170" cy="360" r="3" fill="#14b8a6" opacity="0.3"/>
  <circle cx="160" cy="375" r="3" fill="#0891b2" opacity="0.3"/>
  
  <circle cx="320" cy="100" r="3" fill="#14b8a6" opacity="0.3"/>
  <circle cx="340" cy="110" r="3" fill="#0891b2" opacity="0.3"/>
  <circle cx="330" cy="125" r="3" fill="#14b8a6" opacity="0.3"/>
</svg>