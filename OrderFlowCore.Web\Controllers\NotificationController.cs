using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.Extentions;
using Microsoft.Extensions.Logging;

namespace OrderFlowCore.Web.Controllers;

[Authorize]
public class NotificationController : Controller
{
    private readonly INotificationService _notificationService;
    private readonly ILogger<NotificationController> _logger;

    public NotificationController(
        INotificationService notificationService,
        ILogger<NotificationController> logger)
    {
        _notificationService = notificationService;
        _logger = logger;
    }

    [HttpGet]
    public async Task<IActionResult> GetUnreadNotifications()
    {
        try
        {
            var username = User.GetUsername();
            var userRole = User.GetUserRole();
            var roleType = User.GetUserRoleType();

            _logger.LogInformation("Getting unread notifications for user {Username} with role {UserRole} and roleType {RoleType}", 
                username, userRole, roleType);

            var result = await _notificationService.GetUnreadNotificationsAsync(username, userRole, roleType);

            if (result.IsSuccess)
            {
                return Json(result.Data);
            }
            else
            {
                _logger.LogWarning("Failed to get notifications for user {Username}: {Error}", username, result.Message);
                return Json(new { success = false, error = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting unread notifications");
            return Json(new { success = false, error = "حدث خطأ أثناء جلب الإشعارات" });
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> MarkAsRead(int id)
    {
        try
        {
            var result = await _notificationService.MarkAsReadAsync(id);
            
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking notification {Id} as read", id);
            TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث الإشعار";
        }

        return RedirectToAction("Index", "Dashboard");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> MarkAllAsRead()
    {
        try
        {
            var username = User.GetUsername();
            var result = await _notificationService.MarkAllAsReadAsync(username);
            
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error marking all notifications as read for user {Username}", User.GetUsername());
            TempData["ErrorMessage"] = "حدث خطأ أثناء تحديث الإشعارات";
        }

        return RedirectToAction("Index", "Dashboard");
    }
}
