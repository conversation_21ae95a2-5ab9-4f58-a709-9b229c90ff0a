using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class PathManagementDropdownDataDto
    {
        public List<string> OrderTypes { get; set; } = new List<string>();
        public List<string> JobTypes { get; set; } = new List<string>();
        public List<string> Nationalities { get; set; } = new List<string>();
        public List<string> Supervisors { get; set; } = new List<string>();
    }

    public class ManualRoutingDto
    {
        public Dictionary<string, List<string>> PathsConfiguration { get; set; } = new Dictionary<string, List<string>>();
        public List<string> AvailableSupervisors { get; set; } = new List<string>();
    }

    public class PathConfigurationDto
    {
        public string PathColumn { get; set; }
        public List<string> Supervisors { get; set; } = new List<string>();
    }

    public class UpdatePathSupervisorDto
    {
        public string PathColumn { get; set; }
        public string SupervisorText { get; set; }
        public bool IsChecked { get; set; }
    }
}
