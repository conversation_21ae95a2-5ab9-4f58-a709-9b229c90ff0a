using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IOrdersTypeService
{
    Task<ServiceResult<IEnumerable<OrdersTypeDto>>> GetAllAsync();
    Task<ServiceResult<OrdersTypeDto>> GetByIdAsync(int id);
    Task<ServiceResult> CreateAsync(OrdersTypeDto dto);
    Task<ServiceResult> UpdateAsync(OrdersTypeDto dto);
    Task<ServiceResult> DeleteAsync(int id);

    // Import/Export functionality
    Task<ServiceResult<byte[]>> ExportToExcelAsync();
    Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream);
}
