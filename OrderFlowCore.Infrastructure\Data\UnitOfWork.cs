using Microsoft.EntityFrameworkCore.Storage;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Infrastructure.Repositories;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Data
{
    public class UnitOfWork : IUnitOfWork
    {
        private readonly ApplicationDbContext _context;
        private IDbContextTransaction? _transaction;
        public IUserRepository Users { get; }
        public IOrderRepository Orders { get; }
        public IOrdersTypeRepository OrdersTypes { get; }
        public IEmploymentTypeRepository EmploymentTypes { get; }
        public IJobTypeRepository JobTypes { get; }
        public IQualificationRepository Qualifications { get; }
        public INationalityRepository Nationalities { get; }
        public IDepartmentRepository Departments { get; }
        public IEmployeeRepository Employees { get; }
        public IPredefinedPathRepository PredefinedPaths { get; }
        public IPathsTableRepository PathsTables { get; }
        public IAutoRoutingRepository AutoRoutings { get; }
        public IDirectRoutingRepository DirectRoutings { get; }
        public ISupervisorsFollowUpRepository SupervisorsFollowUps { get; }

        public UnitOfWork(
            ApplicationDbContext context,
            IUserRepository users,
            IOrderRepository orders,
            IOrdersTypeRepository ordersTypes,
            IEmploymentTypeRepository employmentTypes,
            IJobTypeRepository jobTypes,
            IQualificationRepository qualifications,
            INationalityRepository nationalities,
            IDepartmentRepository departments,
            IEmployeeRepository employees,
            IPredefinedPathRepository predefinedPaths,
            IPathsTableRepository pathsTables,
            IAutoRoutingRepository autoRoutings,
            IDirectRoutingRepository directRoutings,
            ISupervisorsFollowUpRepository supervisorsFollowUps)
        {
            _context = context;
            Users = users;
            Orders = orders;
            OrdersTypes = ordersTypes;
            EmploymentTypes = employmentTypes;
            JobTypes = jobTypes;
            Qualifications = qualifications;
            Nationalities = nationalities;
            Departments = departments;
            Employees = employees;
            PredefinedPaths = predefinedPaths;
            PathsTables = pathsTables;
            AutoRoutings = autoRoutings;
            DirectRoutings = directRoutings;
            SupervisorsFollowUps = supervisorsFollowUps;
        }


        public async Task BeginTransactionAsync()
        {
            if (_transaction != null)
                return;

            _transaction = await _context.Database.BeginTransactionAsync();
        }

        public async Task CommitTransactionAsync()
        {
            try
            {
                await _context.SaveChangesAsync();
                await _transaction?.CommitAsync();
            }
            catch
            {
                await RollbackTransactionAsync();
                throw;
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                }
                _transaction = null;
            }
        }

        public async Task RollbackTransactionAsync()
        {
            try
            {
                await _transaction?.RollbackAsync();
            }
            finally
            {
                if (_transaction != null)
                {
                    await _transaction.DisposeAsync();
                }
                _transaction = null;
            }
        }

        public async Task<int> SaveChangesAsync()
        {
            return await _context.SaveChangesAsync();
        }

        public void Dispose()
        {
            _context.Dispose();
            _transaction?.Dispose();
        }
    }
} 