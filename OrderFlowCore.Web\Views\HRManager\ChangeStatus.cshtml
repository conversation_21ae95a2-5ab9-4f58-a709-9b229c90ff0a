@model OrderFlowCore.Web.ViewModels.HRManagerChangeStatusViewModel
@{
    ViewData["Title"] = "تغيير حالة الطلبات - مدير الموارد البشرية";
}

<div class="container mt-4">
    @Html.AntiForgeryToken()
    
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "HRManager")">مدير الموارد البشرية</a></li>
            <li class="breadcrumb-item active" aria-current="page">تغيير حالة الطلبات</li>
        </ol>
    </nav>
    
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">تغيير حالة الطلبات - مدير الموارد البشرية</h2>

            <!-- Back to Main Menu Button -->
            <div class="text-start mb-3">
                <a href="@Url.Action("Index", "HRManager")" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Status Change Section -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-sync-alt me-2"></i>إدارة حالة الطلبات
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Section -->
                    <div class="row g-3 mb-4">
                        <div class="col-12 col-lg-8">
                            <div class="input-group input-group-lg">
                                @Html.TextBoxFor(m => m.SearchTerm, new { @class = "form-control", @id = "txtSearch",
                                    placeholder = "ابحث برقم الطلب / اسم الموظف..." })
                                <span class="input-group-text bg-success text-white">
                                    <i class="fas fa-search fa-fw"></i>
                                </span>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="d-grid gap-2 d-lg-flex">
                                <button type="button" class="btn btn-success flex-grow-1" data-filter="today">
                                    <i class="fas fa-sun me-2"></i>اليوم
                                </button>
                                <button type="button" class="btn btn-success flex-grow-1" data-filter="week">
                                    <i class="fas fa-calendar-week me-2"></i>أسبوع
                                </button>
                                <button type="button" class="btn btn-success flex-grow-1" data-filter="month">
                                    <i class="fas fa-moon me-2"></i>شهر
                                </button>
                                <button type="button" class="btn btn-success flex-grow-1" data-filter="two_months">
                                    <i class="fas fa-calendar-plus me-2"></i>شهرين
                                </button>
                                <button type="button" class="btn btn-success flex-grow-1" data-filter="all">
                                    <i class="fas fa-infinity me-2"></i>الكل
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Status Change Form -->
                    <div class="row g-4">
                        <div class="col-12">
                            <label for="statusChangeOrderSelect" class="form-label">
                                <i class="fas fa-list-ol me-2"></i>اختر الطلب
                            </label>
                            @Html.DropDownListFor(m => m.SelectedStatusChangeOrderId, Model.StatusChangeOrderNumbers, 
                                "-- اختر الطلب --", new { @class = "form-select", @id = "statusChangeOrderSelect"})
                        </div>

                        <!-- Order Details Panel -->
                        <div class="col-12" id="statusChangeDetailsPanel" style="display: none;">
                            <div class="alert alert-light border-2 border-success rounded-3 shadow-sm">
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center p-3 bg-light rounded-2">
                                            <i class="fas fa-info-circle fs-4 text-success me-3"></i>
                                            <div>
                                                <div class="text-muted small mb-1">الحالة الحالية</div>
                                                <div class="h5 mb-0 fw-bold text-success" id="lblCurrentStatus"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="d-flex align-items-center p-3 bg-light rounded-2">
                                            <i class="fas fa-file-alt fs-4 text-success me-3"></i>
                                            <div>
                                                <div class="text-muted small mb-1">نوع الطلب</div>
                                                <div class="h5 mb-0 fw-bold text-success" id="lblOrderType"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <label for="newStatusSelect" class="form-label">
                                <i class="fas fa-arrow-right-arrow-left me-2"></i>الحالة الجديدة
                            </label>
                            @Html.DropDownListFor(m => m.SelectedNewStatus, Model.AvailableStatuses, 
                                new { @class = "form-select", @id = "newStatusSelect"})
                        </div>

                        <div class="col-12">
                            <label for="statusChangeNotes" class="form-label">
                                <i class="fas fa-edit me-2"></i>ملاحظات التغيير (اختياري)
                            </label>
                            @Html.TextAreaFor(m => m.StatusChangeNotes, new { @class = "form-control", @id = "statusChangeNotes",
                                rows = "4", placeholder = "ملاحظات التغيير (اختياري)", style = "resize: none;" })
                        </div>

                        <div class="col-12 text-center mt-5">
                            <button type="button" class="btn btn-success btn-lg px-5 py-3 rounded-pill fw-bold" id="btnChangeStatus">
                                تأكيد التغيير
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <div id="messageContainer" class="mt-3"></div>

            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري المعالجة...</p>
            </div>

            <!-- Order Details Section -->
            @await Html.PartialAsync("_OrderDetailsPartial")
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/hrmanager.js"></script>
} 