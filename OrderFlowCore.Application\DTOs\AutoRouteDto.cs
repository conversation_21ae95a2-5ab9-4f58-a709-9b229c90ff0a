using System;
using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class AutoRouteDto
    {
        public int Id { get; set; }
        public string OrderType { get; set; }
        public string Nationality { get; set; }
        public string Job { get; set; }
        public List<string> Supervisors { get; set; } = new List<string>();
        public bool Status { get; set; }
        public string Notes { get; set; }
        public DateTime? CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? ModifiedAt { get; set; }
        public string ModifiedBy { get; set; }

        // Display properties
        public string SupervisorsDisplay => string.Join("; ", Supervisors);
        public string StatusDisplay => Status ? "نشط" : "غير نشط";
        public int SupervisorsCount => Supervisors?.Count ?? 0;
    }

    public class CreateAutoRouteDto
    {
        public string OrderType { get; set; }
        public string Nationality { get; set; }
        public string Job { get; set; }
        public List<string> Supervisors { get; set; } = new List<string>();
        public bool Status { get; set; } = true;
        public string Notes { get; set; }
    }

    public class UpdateAutoRouteDto
    {
        public string OrderType { get; set; }
        public string Nationality { get; set; }
        public string Job { get; set; }
        public List<string> Supervisors { get; set; } = new List<string>();
        public bool Status { get; set; }
        public string Notes { get; set; }
    }
}