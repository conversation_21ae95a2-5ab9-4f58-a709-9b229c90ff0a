using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class OrderNewViewModel
    {
        [Required(ErrorMessage = "اسم الموظف مطلوب")]
        [Display(Name = "اسم الموظف")]
        public string EmployeeName { get; set; }

        [Required(ErrorMessage = "الوظيفة مطلوبة")]
        [Display(Name = "الوظيفة")]
        public string JobTitle { get; set; }

        [Required(ErrorMessage = "رقم الموظف مطلوب")]
        [Display(Name = "رقم الموظف")]
        public string EmployeeNumber { get; set; }

        [Required(ErrorMessage = "السجل المدني مطلوب")]
        [Display(Name = "السجل المدني")]
        public string CivilRecord { get; set; }

        [Required(ErrorMessage = "الجنسية مطلوبة")]
        [Display(Name = "الجنسية")]
        public string Nationality { get; set; }

        [Required(ErrorMessage = "رقم الجوال مطلوب")]
        [Display(Name = "رقم الجوال")]
        public string MobileNumber { get; set; }

        [Required(ErrorMessage = "القسم مطلوب")]
        [Display(Name = "القسم")]
        public string Department { get; set; }

        [Required(ErrorMessage = "نوع التوظيف مطلوب")]
        [Display(Name = "نوع التوظيف")]
        public string EmploymentType { get; set; }

        [Required(ErrorMessage = "المؤهل مطلوب")]
        [Display(Name = "المؤهل")]
        public string Qualification { get; set; }

        [Required(ErrorMessage = "نوع الطلب مطلوب")]
        [Display(Name = "نوع الطلب")]
        public string OrderType { get; set; }

        [Display(Name = "التفاصيل")]
        public string Details { get; set; }

        [Display(Name = "المرفقات")]
        public List<IFormFile> Attachments { get; set; } = new List<IFormFile>();

        // Dropdown lists
        public List<SelectListItem> Departments { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> JobTitles { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Nationalities { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> EmploymentTypes { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Qualifications { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> OrderTypes { get; set; } = new List<SelectListItem>();

        public static OrderNewDto ToDto(OrderNewViewModel model)
        {
            return new OrderNewDto
            {
                EmployeeName = model.EmployeeName,
                JobTitle = model.JobTitle,
                EmployeeNumber = model.EmployeeNumber,
                CivilRecord = model.CivilRecord,
                Nationality = model.Nationality,
                MobileNumber = model.MobileNumber,
                Department = model.Department,
                EmploymentType = model.EmploymentType,
                Qualification = model.Qualification,
                OrderType = model.OrderType,
                Details = model.Details,
            };
        }

        public static OrderNewViewModel FromDto(OrderNewDto dto)
        {
            return new OrderNewViewModel
            {
                EmployeeName = dto.EmployeeName,
                JobTitle = dto.JobTitle,
                EmployeeNumber = dto.EmployeeNumber,
                CivilRecord = dto.CivilRecord,
                Nationality = dto.Nationality,
                MobileNumber = dto.MobileNumber,
                Department = dto.Department,
                EmploymentType = dto.EmploymentType,
                Qualification = dto.Qualification,
                OrderType = dto.OrderType,
                Details = dto.Details,
            };
        }
    }
} 