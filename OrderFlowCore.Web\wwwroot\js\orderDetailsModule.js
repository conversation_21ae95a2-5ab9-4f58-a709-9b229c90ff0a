/**
 * Order Details Module - Reusable JavaScript module for order details functionality
 * This module provides common functionality for displaying and managing order details
 * across different views in the application.
 */
const OrderDetailsModule = (function () {
    'use strict';

    // Private variables
    let currentOrderId = null;
    let config = {
        showLoading: null,
        hideLoading: null,
        showMessage: null,
        showOrderDetails: null,
        hideOrderDetails: null
    };

    function getStatusBadge(status) {
        if (!status || status.includes('قيد الانتظار')) {
            return '<span class="badge bg-warning text-wrap">قيد الانتظار</span>';
        }

        const normalizedStatus = status.trim();

        const statusMap = [
            { keyword: 'اعتماد', badge: 'bg-success', label: 'اعتماد' },
            { keyword: 'الطلب قيد التنفيذ', badge: 'bg-info', label: 'الطلب قيد التنفيذ' },
            { keyword: 'تمت الإعادة', badge: 'bg-danger', label: 'تمت الإعادة' },
            { keyword: 'تم التحويل', badge: 'bg-secondary', label: 'تم التحويل' }
        ];

        for (const item of statusMap) {
            if (normalizedStatus.includes(item.keyword)) {
                return `<span class="badge ${item.badge} text-wrap">${status}</span>`;
            }
        }

        return '';
    }


    function populateBasicInfo(data) {
        $('#orderNumber').text(data.id || '');
        const orderDate = data.orderDate ? new Date(data.orderDate).toISOString().split('T')[0] : '';
        $('#orderDate').text(orderDate);
        $('#orderStatus').text(data.orderStatus || '');
        $('#orderType').text(data.orderType || '');
    }

    function populateEmployeeInfo(data) {
        $('#employeeName').text(data.employeeName || '');
        $('#department').text(data.department || '');
        $('#jobTitle').text(data.jobTitle || '');
        $('#employmentType').text(data.employmentType || '');
        $('#qualification').text(data.qualification || '');
        $('#employeeNumber').text(data.employeeNumber || '');
        $('#civilRecord').text(data.civilRecord || '');
        $('#nationality').text(data.nationality || '');
        $('#mobileNumber').text(data.mobileNumber || '');

        // Handle details
        if (data.details) {
            $('#detailsText').text(data.details);
            $('#detailsRow').show();
        } else {
            $('#detailsRow').hide();
        }
    }

    function populateApprovalStatus(data) {
        $('#managerApproval').html(getStatusBadge(data.confirmedByDepartmentManager || 'قيد الانتظار'));
        $('#assistantManagerApproval').html(getStatusBadge(data.confirmedByAssistantManager || 'قيد الانتظار'));
        $('#coordinatorApproval').html(getStatusBadge(data.confirmedByCoordinator || 'قيد الانتظار'));

        $('#cancellationReason').text(data.reasonForCancellation);
        $('#coordinatorDetails').text(data.coordinatorDetails);

    }

    function populateSupervisorPermissions(data) {
        // Map supervisor permissions
        const supervisorMap = {
            'employeeServicesPermission': data.supervisorOfEmployeeServices,
            'hrPlanningPermission': data.supervisorOfHumanResourcesPlanning,
            'itPermission': data.supervisorOfInformationTechnology,
            'attendanceControlPermission': data.supervisorOfAttendance,
            'medicalRecordsPermission': data.supervisorOfMedicalRecords,
            'payrollPermission': data.supervisorOfPayrollAndBenefits,
            'legalCompliancePermission': data.supervisorOfLegalAndCompliance,
            'hrServicesPermission': data.supervisorOfHumanResourcesServices,
            'housingPermission': data.supervisorOfHousing,
            'filesSectionPermission': data.supervisorOfFiles,
            'outpatientPermission': data.supervisorOfOutpatientClinics,
            'socialInsurancePermission': data.supervisorOfSocialSecurity,
            'inventoryControlPermission': data.supervisorOfInventoryControl,
            'revenueDevelopmentPermission': data.supervisorOfRevenueDevelopment,
            'securityPermission': data.supervisorOfSecurity,
            'medicalConsultationPermission': data.supervisorOfMedicalConsultation,
        };

        // Populate each supervisor permission
        Object.keys(supervisorMap).forEach(key => {
            const value = supervisorMap[key];
            $(`#${key}`).html(getStatusBadge(value || 'قيد الانتظار'));
        });
    }

    function populateHRManager(data) {
        $('#hrManagerApproval').html(getStatusBadge(data.hrManagerApproval || 'قيد الانتظار'));
    }

    // Public API
    return {
        /**
         * Initialize the module with configuration
         * @param {Object} options - Configuration options
         */
        init: function (options) {
            config = { ...config, ...options };
        },

        /**
         * Get the current configuration
         * @returns {Object} Current configuration
         */
        getConfig: function () {
            return config;
        },

        /**
         * Load order details from server
         * @param {number} orderId - The order ID to load
         * @param {string} endpoint - The endpoint to call
         * @param {Object} additionalData - Additional data to send with the request
         */
        loadOrderDetails: function (orderId, endpoint, additionalData = {}) {
            currentOrderId = orderId;

            this.showLoading();

            const requestData = { orderId: orderId, ...additionalData };

            $.ajax({
                url: endpoint,
                type: 'POST',
                data: requestData,
                success: function (response) {
                    this.hideLoading();

                    if (response.success) {
                        this.populateOrderDetails(response.data);
                        this.showOrderDetails();
                    } else {
                        this.showMessage(response.message || 'حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                    }
                }.bind(this),
                error: function () {
                    this.hideLoading();
                    
                    this.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                }
            });
        },

        /**
         * Populate order details in the DOM
         * @param {Object} data - Order details data
         */
        populateOrderDetails: function (data) {
            populateBasicInfo(data);
            populateEmployeeInfo(data);
            populateApprovalStatus(data);
            populateSupervisorPermissions(data);
            populateHRManager(data);

            // Check for ActionRequire status and show warning
            this.checkActionRequireStatus(data);
        },

        /**
         * Check if order status is ActionRequire and show warning
         * @param {Object} data - Order details data
         */
        checkActionRequireStatus: function (data) {
            const actionRequireContainer = document.getElementById('actionRequireMessageContainer');
            if (!actionRequireContainer) return;

            // Define statuses that require action
            const statusesRequiringAction = new Set([
                'ActionRequire',
                'يتطلب إجراءات',
                'ActionRequiredBySupervisor',
                'يتطلب إجراءات من المشرف'
            ]);

            if (!statusesRequiringAction.has(data.orderStatus)) return;

            const isCoordinatorAction = data.orderStatus == 'ActionRequire' || data.orderStatus == 'يتطلب إجراءات';
            const details = isCoordinatorAction
                ? data.coordinatorDetails || 'لا توجد تفاصيل إضافية'
                : data.supervisorNotes || 'لا توجد تفاصيل إضافية';

            actionRequireContainer.innerHTML = `
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    <div class="d-flex align-items-center">
                        <div class="me-3">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                        <div>
                            <h6 class="alert-heading mb-1"> تنبيه: هذا الطلب يتطلب إجراءات</h6>
                            <p class="mb-1"><strong>التفاصيل:</strong> ${details}</p>
                            <p class="mb-0"><small>نرجو مراجعة الإجراءات المطلوبة قبل المتابعة</small></p>
                        </div>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
        }, 

        /**
         * Get current order ID
         * @returns {number|null} Current order ID
         */
        getCurrentOrderId: function () {
            return currentOrderId;
        },

        /**
         * Show order details section
         */
        showOrderDetails: function () {
            if (config.showOrderDetails) {
                config.showOrderDetails();
                return;
            }

            $('#orderDetails').show();
            $('#btnProcessOrder').prop('disabled', false);  
        },

        /**
         * Hide order details
         */
        hideOrderDetails: function () {
            if (config.hideOrderDetails) {
                config.hideOrderDetails();
                return;
            }

            $('#orderDetails').hide();
            $('#btnProcessOrder').prop('disabled', true);
            currentOrderId = null;
        },

        /**
         * Show loading state
         */
        showLoading: function () {
            if (config.showLoading) {
                config.showLoading();
                return;
            }
            $('#loading').show();
            $('#orderDetails').hide();
        },

        /**
         * Hide loading state
         */
        hideLoading: function () {
            if (config.hideLoading) {
                config.hideLoading();
                return;
            }
            $('#loading').hide();
        },

        /**
         * Show message
         * @param {string} message - Message to display
         * @param {string} type - Message type ('success' or 'error')
         */
        showMessage: function (message, type) {
            if (config.showMessage) {
                config.showMessage(message, type);
                return;
            }

            const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            const icon = type === 'error' ? '❌' : '✅';
            $('#messageContainer').html(`
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${icon} ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `);


            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    $('#messageContainer .alert').fadeOut();
                }, 5000);
            }
        },
    };
})();

// Make it available globally
window.OrderDetailsModule = OrderDetailsModule; 