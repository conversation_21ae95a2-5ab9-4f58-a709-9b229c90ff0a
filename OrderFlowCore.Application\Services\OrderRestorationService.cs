using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;

namespace OrderFlowCore.Application.Services
{
    public class OrderRestorationService : IOrderRestorationService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISupervisorService _supervisorService;
        private readonly ILogger<OrderRestorationService> _logger;

        public OrderRestorationService(
            IUnitOfWork unitOfWork,
            ISupervisorService supervisorService,
            ILogger<OrderRestorationService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<RestorableOrderDto>>> GetRestorableOrdersAsync(string searchTerm, string filter)
        {
            try
            {
                // Get orders that pending for supervisors to be restored to coordinator
                var orders = await _unitOfWork.Orders.GetSupervisorsPendingOrdersAsync();
                
                // Apply search filter
                var filteredOrders = ApplySearchFilter(orders, searchTerm);
                
                // Apply date filter
                var dateFilteredOrders = ApplyDateFilter(filteredOrders, filter);
                
                // Map to DTOs
                var restorableOrders = dateFilteredOrders
                    .OrderByDescending(o => o.CreatedAt)
                    .Select(MapToRestorableOrderDto)
                    .ToList();

                if (restorableOrders.Count == 0)
                {
                    return ServiceResult<List<RestorableOrderDto>>.Failure("لا توجد طلبات قابلة للاستعادة");
                }

                return ServiceResult<List<RestorableOrderDto>>.Success(restorableOrders, $"تم العثور علي {restorableOrders.Count} طلبات قابلة للاستعادة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restorable orders");
                return ServiceResult<List<RestorableOrderDto>>.Failure("حدث خطأ أثناء جلب الطلبات القابلة للاستعادة");
            }
        }

        public async Task<ServiceResult<RestoreDetailsDto>> GetRestoreOrderDetailsAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<RestoreDetailsDto>.Failure("لم يتم العثور على الطلب");
                }

                // Get assigned supervisors using the injected service
                var assignedSupervisors = _supervisorService.GetAssignedSupervisors(order);
                var transferDate = ExtractTransferDate(order.ConfirmedByCoordinator);

                var details = new RestoreDetailsDto
                {
                    CurrentStatus = order.OrderStatus.ToDisplayString(),
                    TransferDate = transferDate,
                    AssignedSupervisors = string.Join(", ", assignedSupervisors),
                    SupervisorCount = assignedSupervisors.Count
                };

                return ServiceResult<RestoreDetailsDto>.Success(details);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting restore order details for {OrderId}", orderId);
                return ServiceResult<RestoreDetailsDto>.Failure("حدث خطأ أثناء جلب تفاصيل الطلب");
            }
        }

        public async Task<ServiceResult> RestoreOrderFromSupervisorsAsync(int orderId, string restoreNotes, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Reset order to coordinator status
                order.OrderStatus = OrderStatus.B;
                order.ConfirmedByCoordinator = OrderHelper.RestoredBy(userName);
                order.CoordinatorDetails = (order.CoordinatorDetails ?? "") + $" | استعادة: {restoreNotes}";

                // Clear supervisor statuses using the service
                _supervisorService.ClearUnderImplementationStatuses(order);

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم استعادة الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error restoring order {OrderId}", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء استعادة الطلب");
            }
        }

        #region Private Helper Methods

        private List<OrdersTable> ApplySearchFilter(List<OrdersTable> orders, string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return orders;

            return orders.Where(o =>
                o.Id.ToString().Contains(searchTerm) ||
                o.EmployeeName.Contains(searchTerm)).ToList();
        }

        private List<OrdersTable> ApplyDateFilter(List<OrdersTable> orders, string filter)
        {
            if (string.IsNullOrWhiteSpace(filter))
                return orders;

            var today = DateTime.Today;

            return filter.ToLower() switch
            {
                "today" => orders.Where(o => o.CreatedAt.Date == today).ToList(),
                "week" => orders.Where(o => o.CreatedAt.Date >= today.AddDays(-(int)today.DayOfWeek)).ToList(),
                "month" => orders.Where(o => o.CreatedAt.Date >= new DateTime(today.Year, today.Month, 1)).ToList(),
                _ => orders
            };
        }

        private RestorableOrderDto MapToRestorableOrderDto(OrdersTable order)
        {
            return new RestorableOrderDto
            {
                Id = order.Id,
                OrderNumber = order.Id.ToString(),
                EmployeeName = order.EmployeeName,
                Department = order.Department,
                OrderDate = order.CreatedAt.ToString("yyyy-MM-dd"),
                OrderStatus = order.OrderStatus,
                DisplayText = $"{order.Id} | {order.EmployeeName} | {order.Department}"
            };
        }

        private string ExtractTransferDate(string confirmedByCoordinator)
        {
            if (string.IsNullOrEmpty(confirmedByCoordinator))
                return string.Empty;

            var parts = confirmedByCoordinator.Split('|');
            return parts.Length > 0 ? parts[0] : string.Empty;
        }

        #endregion
    }
}
