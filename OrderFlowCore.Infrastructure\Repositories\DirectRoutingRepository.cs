using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class DirectRoutingRepository : IDirectRoutingRepository
    {
        private readonly ApplicationDbContext _context;

        public DirectRoutingRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<DirectRouting>> GetAllAsync()
        {
            return await _context.DirectRoutings
                .AsNoTracking()
                .OrderByDescending(dr => dr.CreatedAt)
                .ToListAsync();
        }

        public async Task<DirectRouting?> GetByIdAsync(int id)
        {
            return await _context.DirectRoutings.FindAsync(id);
        }

        public async Task<DirectRouting> AddAsync(DirectRouting entity)
        {
            entity.CreatedAt = DateTime.UtcNow;
            _context.DirectRoutings.Add(entity);
            await _context.SaveChangesAsync();
            return entity;
        }

        public async Task UpdateAsync(DirectRouting entity)
        {
            entity.ModifiedAt = DateTime.UtcNow;
            _context.DirectRoutings.Update(entity);
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var entity = await GetByIdAsync(id);
            if (entity != null)
            {
                _context.DirectRoutings.Remove(entity);
                await _context.SaveChangesAsync();
            }
        }

        public async Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null)
        {
            var query = _context.DirectRoutings
                .Where(dr => dr.OrderType == orderType && 
                           dr.Nationality == nationality && 
                           dr.Status == true);

            if (excludeId.HasValue)
            {
                query = query.Where(dr => dr.Id != excludeId.Value);
            }

            // Check for job 
            var count = await query.CountAsync(dr =>
                dr.Job == "الكل" ||
                dr.Job == job);

            return count > 0;
        }

        public async Task<int> GetActiveCountAsync()
        {
            return await _context.DirectRoutings.CountAsync(dr => dr.Status == true);
        }

        public async Task<List<DirectRouting>> GetActiveRoutesAsync()
        {
            return await _context.DirectRoutings
                .Where(dr => dr.Status == true)
                .AsNoTracking()
                .OrderByDescending(dr => dr.CreatedAt)
                .ToListAsync();
        }

        public async Task<DirectRouting?> GetMatchingRouteAsync(string orderType, string nationality, string job)
        {
            return await _context.DirectRoutings
                .Where(dr => dr.OrderType == orderType && 
                           dr.Nationality == nationality && 
                           dr.Status == true)
                .Where(dr =>
                    dr.Job == "الكل" ||
                    dr.Job == job)
                .FirstOrDefaultAsync();
        }
    }
}
