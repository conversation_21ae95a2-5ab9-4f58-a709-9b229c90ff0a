using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IStatisticsService
    {
        Task<ServiceResult<GeneralStatisticsDto>> GetGeneralStatisticsAsync();
        Task<ServiceResult<StageStatisticsDto>> GetStageStatisticsAsync();
        Task<ServiceResult<List<OrderDepartmentStatisticsDto>>> GetDepartmentStatisticsAsync();
        Task<ServiceResult<List<SupervisorStatisticsDto>>> GetSupervisorStatisticsAsync();
        Task<ServiceResult<List<AssistantManagerStatisticsDto>>> GetAssistantManagerStatisticsAsync();
        Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetTransferTypeStatisticsAsync();
    }
}