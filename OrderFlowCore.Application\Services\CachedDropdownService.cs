using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;

namespace OrderFlowCore.Application.Services
{
    public interface ICachedDropdownService
    {
        Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync();
        Task InvalidateDropdownCacheAsync();
    }

    public class CachedDropdownService : ICachedDropdownService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMemoryCache _cache;
        private readonly ILogger<CachedDropdownService> _logger;
        
        private const string DROPDOWN_CACHE_KEY = "dropdown_data";
        private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(30);

        public CachedDropdownService(
            IUnitOfWork unitOfWork,
            IMemoryCache cache,
            ILogger<CachedDropdownService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<DropdownDataDto>> GetDropdownDataAsync()
        {
            try
            {
                _logger.LogDebug("Attempting to get dropdown data from cache");

                if (_cache.TryGetValue(DROPDOWN_CACHE_KEY, out DropdownDataDto cachedData))
                {
                    _logger.LogDebug("Dropdown data found in cache");
                    return ServiceResult<DropdownDataDto>.Success(cachedData);
                }

                _logger.LogDebug("Dropdown data not found in cache, loading from database");

                var dropdownData = new DropdownDataDto();

                // Load all dropdown data
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
                var employmentTypes = await _unitOfWork.EmploymentTypes.GetAllAsync();
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                var ordersTypes = await _unitOfWork.OrdersTypes.GetAllAsync();
                var qualifications = await _unitOfWork.Qualifications.GetAllAsync();

                // Map to dropdown items
                dropdownData.Departments = departments.Select(d => new DropdownItemDto 
                { 
                    Value = d.Id.ToString(), 
                    Text = d.Name 
                }).ToList();

                dropdownData.Nationalities = nationalities.Select(n => new DropdownItemDto 
                { 
                    Value = n.Id.ToString(), 
                    Text = n.Name 
                }).ToList();

                dropdownData.EmploymentTypes = employmentTypes.Select(e => new DropdownItemDto 
                { 
                    Value = e.Id.ToString(), 
                    Text = e.Name 
                }).ToList();

                dropdownData.JobTitles = jobTypes.Select(j => new DropdownItemDto 
                { 
                    Value = j.Id.ToString(), 
                    Text = j.Name 
                }).ToList();

                dropdownData.OrderTypes = ordersTypes.Select(o => new DropdownItemDto 
                { 
                    Value = o.Id.ToString(), 
                    Text = o.Name 
                }).ToList();

                dropdownData.Qualifications = qualifications.Select(q => new DropdownItemDto 
                { 
                    Value = q.Id.ToString(), 
                    Text = q.Name 
                }).ToList();

                // Cache the data
                var cacheEntryOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = _cacheExpiration,
                    SlidingExpiration = TimeSpan.FromMinutes(10),
                    Priority = CacheItemPriority.High
                };

                _cache.Set(DROPDOWN_CACHE_KEY, dropdownData, cacheEntryOptions);

                _logger.LogInformation("Dropdown data loaded from database and cached for {CacheExpiration}", _cacheExpiration);

                return ServiceResult<DropdownDataDto>.Success(dropdownData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dropdown data");
                return ServiceResult<DropdownDataDto>.Failure($"Error loading dropdown data: {ex.Message}");
            }
        }

        public async Task InvalidateDropdownCacheAsync()
        {
            try
            {
                _cache.Remove(DROPDOWN_CACHE_KEY);
                _logger.LogInformation("Dropdown cache invalidated");
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error invalidating dropdown cache");
            }
        }
    }

    // Performance monitoring service
    public interface IPerformanceMonitoringService
    {
        void TrackOperation(string operationName, TimeSpan duration);
        void TrackDatabaseQuery(string queryName, TimeSpan duration);
        void TrackFileOperation(string operationType, long fileSize, TimeSpan duration);
        Task<Dictionary<string, object>> GetPerformanceMetricsAsync();
    }

    public class PerformanceMonitoringService : IPerformanceMonitoringService
    {
        private readonly ILogger<PerformanceMonitoringService> _logger;
        private readonly IMemoryCache _cache;
        
        private const string METRICS_CACHE_KEY = "performance_metrics";
        private readonly object _lockObject = new object();

        public PerformanceMonitoringService(
            ILogger<PerformanceMonitoringService> logger,
            IMemoryCache cache)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        }

        public void TrackOperation(string operationName, TimeSpan duration)
        {
            _logger.LogInformation("Operation {OperationName} completed in {Duration}ms", 
                operationName, duration.TotalMilliseconds);

            UpdateMetrics($"operation_{operationName}", duration);
        }

        public void TrackDatabaseQuery(string queryName, TimeSpan duration)
        {
            _logger.LogDebug("Database query {QueryName} completed in {Duration}ms", 
                queryName, duration.TotalMilliseconds);

            UpdateMetrics($"db_query_{queryName}", duration);
        }

        public void TrackFileOperation(string operationType, long fileSize, TimeSpan duration)
        {
            _logger.LogInformation("File operation {OperationType} for {FileSize} bytes completed in {Duration}ms", 
                operationType, fileSize, duration.TotalMilliseconds);

            UpdateMetrics($"file_{operationType}", duration);
        }

        public async Task<Dictionary<string, object>> GetPerformanceMetricsAsync()
        {
            await Task.CompletedTask;
            
            if (_cache.TryGetValue(METRICS_CACHE_KEY, out Dictionary<string, object> metrics))
            {
                return metrics;
            }

            return new Dictionary<string, object>();
        }

        private void UpdateMetrics(string metricName, TimeSpan duration)
        {
            lock (_lockObject)
            {
                var metrics = _cache.Get<Dictionary<string, object>>(METRICS_CACHE_KEY) 
                    ?? new Dictionary<string, object>();

                var key = $"{metricName}_avg_duration";
                var countKey = $"{metricName}_count";

                if (metrics.ContainsKey(key))
                {
                    var currentAvg = (double)metrics[key];
                    var currentCount = (int)metrics[countKey];
                    var newCount = currentCount + 1;
                    var newAvg = ((currentAvg * currentCount) + duration.TotalMilliseconds) / newCount;
                    
                    metrics[key] = newAvg;
                    metrics[countKey] = newCount;
                }
                else
                {
                    metrics[key] = duration.TotalMilliseconds;
                    metrics[countKey] = 1;
                }

                _cache.Set(METRICS_CACHE_KEY, metrics, TimeSpan.FromHours(1));
            }
        }
    }
}
