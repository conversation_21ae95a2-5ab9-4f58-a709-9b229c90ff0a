using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface INotificationService
{
    Task<ServiceResult<NotificationResponseDto>> GetUnreadNotificationsAsync(string username, UserRole userRole, string? roleType);
    Task<ServiceResult<OrderNotificationDto>> GetOrderNotificationAsync(string username, UserRole userRole, string? roleType);
    Task<ServiceResult> MarkAsReadAsync(int notificationId);
    Task<ServiceResult> MarkAllAsReadAsync(string username);
}
