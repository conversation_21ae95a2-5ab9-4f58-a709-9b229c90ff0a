@model OrderFlowCore.Web.ViewModels.StatisticsViewModel
@{
    ViewData["Title"] = "الإحصائيات";
}

<div class="container-fluid mt-4">
    <!-- Page Header -->
    <div class="welcome-header mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-chart-bar ml-2"></i> الإحصائيات</h2>
                <p>إحصائيات شاملة لجميع الطلبات والمراحل</p>
            </div>
            <div class="col-md-4 text-end">
                <button id="refreshStats" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-sync-alt"></i> تحديث
                </button>
                <button id="exportStats" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-download"></i> تصدير
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="text-center d-none">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>

    <!-- Messages -->
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle ml-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    }

    <!-- العمود الأول: إحصائيات عامة -->
    <div class="card mb-4">
        <div class="card-header bg-primary text-white">
            <h3 class="card-title mb-0">إحصائيات عامة</h3>
        </div>
        <div class="card-body">
            <!-- إحصائيات إجمالية -->
            <div class="summary-stats mb-4">
                <div class="row g-3">
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">إجمالي الطلبات</div>
                            <div class="stat-value text-primary" data-animate="counter">
                                @Model.GeneralStatistics.TotalRequests
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">الطلبات المنجزة</div>
                            <div class="stat-value text-success" data-animate="counter">
                                @Model.GeneralStatistics.CompletedRequests
                            </div>
                            <div class="stat-percentage">
                                @if (Model.GeneralStatistics.TotalRequests > 0)
                                {
                                    <small class="text-muted">
                                        (@(Math.Round((double)Model.GeneralStatistics.CompletedRequests / Model.GeneralStatistics.TotalRequests * 100, 1))%)
                                    </small>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">تحت التنفيذ</div>
                            <div class="stat-value text-warning" data-animate="counter">
                                @Model.GeneralStatistics.PendingRequests
                            </div>
                            <div class="stat-percentage">
                                @if (Model.GeneralStatistics.TotalRequests > 0)
                                {
                                    <small class="text-muted">
                                        (@(Math.Round((double)Model.GeneralStatistics.PendingRequests / Model.GeneralStatistics.TotalRequests * 100, 1))%)
                                    </small>
                                }
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 col-lg-3">
                        <div class="stat-box border rounded p-3 text-center">
                            <div class="stat-label">متوسط زمن الإنجاز</div>
                            <div class="stat-value text-info" data-animate="decimal">
                                @Model.GeneralStatistics.AverageTimeToComplete أيام
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- إحصائيات المراحل -->
            <div class="stages-stats">
                <h4 class="mb-3">توزيع الطلبات حسب المراحل</h4>
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-responsive">
                            <table class="table table-hover border">
                                <tbody>
                                    <tr data-stage="dm">
                                        <td><strong>مدير القسم</strong></td>
                                        <td class="stage-value">@Model.StageStatistics.DepartmentManager</td>
                                        <td class="stage-bar">
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-secondary" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.HRCoordinator / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr data-stage="c">
                                        <td><strong>المشرفين</strong></td>
                                        <td class="stage-value">@Model.StageStatistics.Supervisors</td>
                                        <td class="stage-bar">
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-dark" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.Supervisors / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr data-stage="d">
                                        <td><strong>مدير الموارد البشرية</strong></td>
                                        <td class="stage-value">@Model.StageStatistics.HRManager</td>
                                        <td class="stage-bar">
                                            <div class="progress" style="height: 8px;">
                                                <div class="progress-bar bg-primary" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.HRManager / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <canvas id="stageChart" width="300" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card">
        <div class="card-header bg-light">
            <ul class="nav nav-tabs card-header-tabs" role="tablist">
                <li class="nav-item">
                    <a class="nav-link active" data-bs-toggle="tab" href="#departmentStats">
                        <i class="fas fa-building ml-1"></i>
                        إحصائيات الأقسام
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#supervisors">
                        <i class="fas fa-users-cog ml-1"></i>
                        إحصائيات المشرفين
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#assistantManagers">
                        <i class="fas fa-user-tie ml-1"></i>
                        إحصائيات مساعدي المدير
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-bs-toggle="tab" href="#transferTypes">
                        <i class="fas fa-exchange-alt ml-1"></i>
                        نوع التحويل
                    </a>
                </li>
            </ul>
        </div>

        <div class="card-body">
            <div class="tab-content">
                <!-- تبويب إحصائيات الأقسام -->
                <div id="departmentStats" class="tab-pane fade show active">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">إحصائيات الأقسام التفصيلية</h3>
                            <button class="btn btn-outline-light btn-sm" onclick="toggleDepartmentView()">
                                <i class="fas fa-chart-bar"></i> عرض الرسم البياني
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="summary-stats bg-light p-3 rounded mb-4">
                                <div class="row">
                                    <div class="col-md-6">
                                        <strong class="completed">إجمالي الأقسام: @Model.DepartmentStatistics.Count</strong>
                                    </div>
                                    <div class="col-md-6">
                                        <strong class="avg-completion-time">إجمالي الطلبات: @Model.DepartmentStatistics.Sum(d => d.TotalOrders)</strong>
                                    </div>
                                </div>
                            </div>

                            <!-- Table View -->
                            <div id="departmentTableView">
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <input type="text" id="departmentSearch" class="form-control" placeholder="البحث في الأقسام...">
                                    </div>
                                    <div class="col-md-6">
                                        <select id="departmentSort" class="form-select">
                                            <option value="total">ترتيب حسب إجمالي الطلبات</option>
                                            <option value="completed">ترتيب حسب الطلبات المنجزة</option>
                                            <option value="pending">ترتيب حسب الطلبات قيد التنفيذ</option>
                                            <option value="name">ترتيب حسب اسم القسم</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                                    @foreach (var dept in Model.DepartmentStatistics)
                                    {
                                        <div class='department-row' data-department='@dept.DepartmentName.ToLower()'>
                                            <div class="row align-items-center">
                                                <div class="col-md-4">
                                                    <strong>@dept.DepartmentName</strong>
                                                </div>
                                                <div class="col-md-8">
                                                    <div class="row text-center">
                                                        <div class="col-3">
                                                            <span class='text-primary'>إجمالي: @dept.TotalOrders</span>
                                                        </div>
                                                        <div class="col-3">
                                                            <span class='text-success'>منجز: @dept.CompletedOrders</span>
                                                        </div>
                                                        <div class="col-3">
                                                            <span class='text-warning'>تحت التنفيذ: @dept.PendingOrders</span>
                                                        </div>
                                                        <div class="col-3">
                                                            <span class='text-danger'>ملغي: @dept.CancelledOrders</span>
                                                        </div>
                                                    </div>
                                                    <div class="progress mt-2" style="height: 5px;">
                                                        <div class="progress-bar bg-success" style="width: @(dept.TotalOrders > 0 ? (double)dept.CompletedOrders / dept.TotalOrders * 100 : 0)%"></div>
                                                        <div class="progress-bar bg-warning" style="width: @(dept.TotalOrders > 0 ? (double)dept.PendingOrders / dept.TotalOrders * 100 : 0)%"></div>
                                                        <div class="progress-bar bg-danger" style="width: @(dept.TotalOrders > 0 ? (double)dept.CancelledOrders / dept.TotalOrders * 100 : 0)%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Chart View (Initially Hidden) -->
                            <div id="departmentChartView" class="d-none">
                                <canvas id="departmentChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب إحصائيات المشرفين -->
                <div id="supervisors" class="tab-pane fade">
                    <div class="card h-100">
                        <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">إحصائيات المشرفين التفصيلية</h3>
                            <button class="btn btn-outline-light btn-sm" onclick="toggleSupervisorView()">
                                <i class="fas fa-chart-line"></i> عرض الرسم البياني
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="summary-stats bg-light p-3 rounded mb-4">
                                <div class="row text-center">
                                    <div class="col-md-2">
                                        <strong class='under-execution'>تحت التنفيذ: @Model.SupervisorStatistics.Sum(s => s.UnderExecution)</strong>
                                    </div>
                                    <div class="col-md-2">
                                        <strong class='completed'>منجز: @Model.SupervisorStatistics.Sum(s => s.Completed)</strong>
                                    </div>
                                    <div class="col-md-2">
                                        <strong class='text-warning'>يتطلب إجراءات: @Model.SupervisorStatistics.Sum(s => s.NeedsAction)</strong>
                                    </div>
                                    <div class="col-md-2">
                                        <strong class='text-danger'>معاد: @Model.SupervisorStatistics.Sum(s => s.Returned)</strong>
                                    </div>
                                    <div class="col-md-4">
                                        <strong class='avg-completion-time'>متوسط زمن الإنجاز: @(Model.SupervisorStatistics.Any() ? Model.SupervisorStatistics.Where(s => s.AverageCompletionTime > 0).DefaultIfEmpty().Average(s => s?.AverageCompletionTime ?? 0).ToString("F1") : "0") أيام</strong>
                                    </div>
                                </div>
                            </div>

                            <!-- Search and Filter -->
                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <input type="text" id="supervisorSearch" class="form-control" placeholder="البحث في المشرفين...">
                                </div>
                                <div class="col-md-4">
                                    <select id="supervisorSort" class="form-select">
                                        <option value="completed">ترتيب حسب المنجز</option>
                                        <option value="pending">ترتيب حسب قيد التنفيذ</option>
                                        <option value="time">ترتيب حسب زمن الإنجاز</option>
                                        <option value="name">ترتيب حسب الاسم</option>
                                    </select>
                                </div>
                            </div>

                            <div id="supervisorTableView">
                                <div class="supervisor-list" style="max-height: 400px; overflow-y: auto;">
                                    @foreach (var supervisor in Model.SupervisorStatistics)
                                    {
                                        <div class='supervisor-row' data-supervisor='@supervisor.SupervisorName.ToLower()'>
                                            <div class="card mb-2">
                                                <div class="card-body p-3">
                                                    <div class="row align-items-center">
                                                        <div class="col-md-3">
                                                            <strong>@supervisor.SupervisorName</strong>
                                                        </div>
                                                        <div class="col-md-7">
                                                            <div class="row text-center">
                                                                <div class="col-3">
                                                                    <span class='text-warning'>تحت التنفيذ: @supervisor.UnderExecution</span>
                                                                </div>
                                                                <div class="col-3">
                                                                    <span class='text-success'>منجز: @supervisor.Completed</span>
                                                                </div>
                                                                <div class="col-3">
                                                                    <span class='text-warning'>يتطلب إجراءات: @supervisor.NeedsAction</span>
                                                                </div>
                                                                <div class="col-3">
                                                                    <span class='text-danger'>معاد: @supervisor.Returned</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-2 text-center">
                                                            <span class='avg-completion-time'>@supervisor.AverageCompletionTime.ToString("F1") أيام</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Chart View (Initially Hidden) -->
                            <div id="supervisorChartView" class="d-none">
                                <canvas id="supervisorChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- تبويب مساعدي المدير -->
                <div id="assistantManagers" class="tab-pane fade">
                    <div class="card h-100">
                        <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">إحصائيات مساعدي المدير</h3>
                        </div>
                        <div class="card-body">
                            <div class="summary-stats bg-light p-3 rounded mb-4">
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <strong class='under-execution'>تحت التنفيذ: @Model.AssistantManagerStatistics.Sum(s => s.UnderExecution)</strong>
                                    </div>
                                    <div class="col-md-3">
                                        <strong class='completed'>منجز: @Model.AssistantManagerStatistics.Sum(s => s.Completed)</strong>
                                    </div>
                                    <div class="col-md-3">
                                        <strong class='text-danger'>معاد: @Model.AssistantManagerStatistics.Sum(s => s.Returned)</strong>
                                    </div>
                                    <div class="col-md-3">
                                        <strong class='text-muted'>ملغي: @Model.AssistantManagerStatistics.Sum(s => s.Cancelled)</strong>
                                    </div>
                                </div>
                            </div>

                            <div class="assistant-manager-list" style="max-height: 400px; overflow-y: auto;">
                                @foreach (var am in Model.AssistantManagerStatistics)
                                {
                                    <div class='assistant-manager-row'>
                                        <div class="card mb-2">
                                            <div class="card-body p-3">
                                                <div class="row align-items-center">
                                                    <div class="col-md-3">
                                                        <strong>@am.AssistantManagerName</strong>
                                                    </div>
                                                    <div class="col-md-7">
                                                        <div class="row text-center">
                                                            <div class="col-3">
                                                                <span class='text-warning'>تحت التنفيذ: @am.UnderExecution</span>
                                                            </div>
                                                            <div class="col-3">
                                                                <span class='text-success'>منجز: @am.Completed</span>
                                                            </div>
                                                            <div class="col-3">
                                                                <span class='text-danger'>معاد: @am.Returned</span>
                                                            </div>
                                                            <div class="col-3">
                                                                <span class='text-muted'>ملغي: @am.Cancelled</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-2 text-center">
                                                        <span class='avg-completion-time'>@am.AverageCompletionTime.ToString("F1") أيام</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>


                <!-- تبويب نوع التحويل -->
                <div id="transferTypes" class="tab-pane fade">
                    <div class="card h-100">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h3 class="card-title mb-0">إحصائيات نوع التحويل</h3>
                            <button class="btn btn-outline-light btn-sm" onclick="toggleTransferView()">
                                <i class="fas fa-chart-pie"></i> عرض الرسم البياني
                            </button>
                        </div>
                        <div class="card-body">
                            <div class="summary-stats bg-light p-3 rounded mb-4">
                                <div class="row text-center">
                                    <div class="col-md-6">
                                        <strong class='completed'>إجمالي التحويلات: @Model.TransferTypeStatistics.Sum(t => t.Count)</strong>
                                    </div>
                                    <div class="col-md-6">
                                        <strong class='avg-completion-time'>عدد الأنواع: @Model.TransferTypeStatistics.Count</strong>
                                    </div>
                                </div>
                            </div>

                            <div id="transferTableView">
                                <div class="department-list" style="max-height: 400px; overflow-y: auto;">
                                    @foreach (var transferType in Model.TransferTypeStatistics)
                                    {
                                        <div class='department-row'>
                                            <div class="row align-items-center">
                                                <div class="col-md-6">
                                                    <strong>@transferType.TransferType</strong>
                                                </div>
                                                <div class="col-md-3">
                                                    <span class='text-primary'>العدد: @transferType.Count</span>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="progress" style="height: 8px;">
                                                        <div class="progress-bar bg-info" style="width: @(Model.TransferTypeStatistics.Sum(t => t.Count) > 0 ? (double)transferType.Count / Model.TransferTypeStatistics.Sum(t => t.Count) * 100 : 0)%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Chart View (Initially Hidden) -->
                            <div id="transferChartView" class="d-none">
                                <canvas id="transferChart" width="400" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

<script>
    // Animation for counters
    function animateCounters() {
        const counters = document.querySelectorAll('[data-animate="counter"]');
        counters.forEach(counter => {
            const target = parseInt(counter.textContent);
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    counter.textContent = target;
                    clearInterval(timer);
                } else {
                    counter.textContent = Math.floor(current);
                }
            }, 20);
        });
    }

    // Charts
    let stageChart, departmentChart, supervisorChart, transferChart;

    function initializeCharts() {
        // Stage Chart
        const stageCtx = document.getElementById('stageChart');
        if (stageCtx) {
            stageChart = new Chart(stageCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مدير القسم', 'مساعد مدير A1', 'مساعد مدير A2', 'مساعد مدير A3', 'مساعد مدير A4', 'منسق HR', 'المشرفين', 'مدير HR'],
                    datasets: [{
                        data: [
                            @Model.StageStatistics.DepartmentManager,
                            @Model.StageStatistics.AssistantManagerA1,
                            @Model.StageStatistics.AssistantManagerA2,
                            @Model.StageStatistics.AssistantManagerA3,
                            @Model.StageStatistics.AssistantManagerA4,
                            @Model.StageStatistics.HRCoordinator,
                            @Model.StageStatistics.Supervisors,
                            @Model.StageStatistics.HRManager
                        ],
                        backgroundColor: [
                            '#007bff', '#17a2b8', '#ffc107', '#28a745',
                            '#dc3545', '#6c757d', '#343a40', '#007bff'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                font: { size: 10 }
                            }
                        }
                    }
                }
            });
        }
    }

    // Search and Filter Functions
    function initializeSearchAndFilter() {
        // Department Search
        const departmentSearch = document.getElementById('departmentSearch');
        if (departmentSearch) {
            departmentSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('.department-row');
                rows.forEach(row => {
                    const departmentName = row.getAttribute('data-department');
                    row.style.display = departmentName.includes(searchTerm) ? '' : 'none';
                });
            });
        }

        // Supervisor Search
        const supervisorSearch = document.getElementById('supervisorSearch');
        if (supervisorSearch) {
            supervisorSearch.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('.supervisor-row');
                rows.forEach(row => {
                    const supervisorName = row.getAttribute('data-supervisor');
                    row.style.display = supervisorName.includes(searchTerm) ? '' : 'none';
                });
            });
        }
    }

    // Toggle View Functions
    function toggleDepartmentView() {
        const tableView = document.getElementById('departmentTableView');
        const chartView = document.getElementById('departmentChartView');

        if (tableView.classList.contains('d-none')) {
            tableView.classList.remove('d-none');
            chartView.classList.add('d-none');
        } else {
            tableView.classList.add('d-none');
            chartView.classList.remove('d-none');

            // Initialize department chart if not already done
            if (!departmentChart) {
                const ctx = document.getElementById('departmentChart');
                departmentChart = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: [@Html.Raw(string.Join(",", Model.DepartmentStatistics.Select(d => $"'{d.DepartmentName}'")))],
                        datasets: [
                            {
                                label: 'إجمالي',
                                data: [@string.Join(",", Model.DepartmentStatistics.Select(d => d.TotalOrders))],
                                backgroundColor: '#007bff'
                            },
                            {
                                label: 'منجز',
                                data: [@string.Join(",", Model.DepartmentStatistics.Select(d => d.CompletedOrders))],
                                backgroundColor: '#28a745'
                            },
                            {
                                label: 'قيد التنفيذ',
                                data: [@string.Join(",", Model.DepartmentStatistics.Select(d => d.PendingOrders))],
                                backgroundColor: '#ffc107'
                            }
                        ]
                    },
                    options: {
                        responsive: true,
                        scales: {
                            x: {
                                ticks: {
                                    maxRotation: 45,
                                    font: { size: 10 }
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    function toggleSupervisorView() {
        const tableView = document.getElementById('supervisorTableView');
        const chartView = document.getElementById('supervisorChartView');

        if (tableView.classList.contains('d-none')) {
            tableView.classList.remove('d-none');
            chartView.classList.add('d-none');
        } else {
            tableView.classList.add('d-none');
            chartView.classList.remove('d-none');

            if (!supervisorChart) {
                const ctx = document.getElementById('supervisorChart');
                supervisorChart = new Chart(ctx, {
                    type: 'horizontalBar',
                    data: {
                        labels: [@Html.Raw(string.Join(",", Model.SupervisorStatistics.Select(s => $"'{s.SupervisorName}'")))],
                        datasets: [{
                            label: 'متوسط زمن الإنجاز (أيام)',
                            data: [@string.Join(",", Model.SupervisorStatistics.Select(s => s.AverageCompletionTime))],
                            backgroundColor: '#17a2b8'
                        }]
                    },
                    options: {
                        responsive: true,
                        indexAxis: 'y',
                        scales: {
                            y: {
                                ticks: {
                                    font: { size: 10 }
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    function toggleTransferView() {
        const tableView = document.getElementById('transferTableView');
        const chartView = document.getElementById('transferChartView');

        if (tableView.classList.contains('d-none')) {
            tableView.classList.remove('d-none');
            chartView.classList.add('d-none');
        } else {
            tableView.classList.add('d-none');
            chartView.classList.remove('d-none');

            if (!transferChart) {
                const ctx = document.getElementById('transferChart');
                transferChart = new Chart(ctx, {
                    type: 'pie',
                    data: {
                        labels: [@Html.Raw(string.Join(",", Model.TransferTypeStatistics.Select(t => $"'{t.TransferType}'")))],
                        datasets: [{
                            data: [@string.Join(",", Model.TransferTypeStatistics.Select(t => t.Count))],
                            backgroundColor: [
                                '#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8',
                                '#6c757d', '#343a40', '#e83e8c', '#20c997', '#fd7e14'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'right',
                                labels: {
                                    font: { size: 10 }
                                }
                            }
                        }
                    }
                });
            }
        }
    }

    // Refresh Statistics
    document.getElementById('refreshStats')?.addEventListener('click', function() {
        const spinner = document.getElementById('loadingSpinner');
        spinner.classList.remove('d-none');

        // Simulate refresh (you can implement actual AJAX call here)
        setTimeout(() => {
            location.reload();
        }, 1000);
    });

    // Export Statistics
    document.getElementById('exportStats')?.addEventListener('click', function() {
        // Implement export functionality here
        alert('تصدير الإحصائيات - قيد التطوير');
    });

    // Initialize everything when DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        animateCounters();
        initializeCharts();
        initializeSearchAndFilter();
    });
</script>

<style>
    .stat-box {
        background: #f8f9fa;
        transition: transform 0.2s, box-shadow 0.2s;
        border-radius: 8px;
    }

        .stat-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .stat-value {
        font-size: 1.8rem;
        font-weight: bold;
        line-height: 1.2;
    }

    .stat-percentage {
        margin-top: 0.25rem;
    }

    .department-row, .supervisor-row {
        padding: 0.75rem;
        border-bottom: 1px solid #dee2e6;
        margin-bottom: 0.5rem;
        transition: background-color 0.2s;
    }

        .department-row:hover, .supervisor-row:hover {
            background-color: #f8f9fa;
        }

        .department-row:last-child, .supervisor-row:last-child {
            border-bottom: none;
        }

    .stage-value {
        font-weight: bold;
        color: #007bff;
        font-size: 1.1rem;
    }

    .stage-bar {
        width: 100px;
    }

    .summary-stats {
        font-size: 0.95rem;
    }

    .completed {
        color: #28a745;
    }

    .under-execution {
        color: #ffc107;
    }

    .avg-completion-time {
        color: #17a2b8;
    }

    .text-warning {
        color: #ffc107 !important;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .text-success {
        color: #28a745 !important;
    }

    .text-primary {
        color: #007bff !important;
    }

    .text-info {
        color: #17a2b8 !important;
    }

    /* Custom scrollbar */
    .department-list::-webkit-scrollbar,
    .supervisor-list::-webkit-scrollbar {
        width: 6px;
    }

    .department-list::-webkit-scrollbar-track,
    .supervisor-list::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .department-list::-webkit-scrollbar-thumb,
    .supervisor-list::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;
    }

        .department-list::-webkit-scrollbar-thumb:hover,
        .supervisor-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

    /* Responsive improvements */
    @@media (max-width: 768px) {
        .stat-value

    {
        font-size: 1.4rem;
    }

    .stage-bar {
        width: 60px;
    }

    .department-row .row,
    .supervisor-row .row {
        text-align: center;
    }

    .department-row .col-md-8 .row,
    .supervisor-row .col-md-7 .row {
        margin-top: 0.5rem;
    }

    }

    /* Loading animation */
    .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    /* Progress bars enhancement */
    .progress {
        background-color: #e9ecef;
        border-radius: 4px;
    }

    .progress-bar {
        transition: width 0.6s ease;
    }

    /* Tab enhancements */
    .nav-tabs .nav-link {
        border-radius: 0.375rem 0.375rem 0 0;
        transition: all 0.2s ease;
    }

        .nav-tabs .nav-link:hover {
            background-color: #f8f9fa;
        }

        .nav-tabs .nav-link.active {
            background-color: #fff;
            border-color: #dee2e6 #dee2e6 #fff;
        }
</style>