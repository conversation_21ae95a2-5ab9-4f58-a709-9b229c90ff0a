using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Services;
using System.Diagnostics;

namespace OrderFlowCore.Web.Middleware
{
    public class PerformanceMonitoringMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PerformanceMonitoringMiddleware> _logger;

        public PerformanceMonitoringMiddleware(RequestDelegate next, ILogger<PerformanceMonitoringMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IPerformanceMonitoringService performanceService)
        {
            var stopwatch = Stopwatch.StartNew();
            var requestPath = context.Request.Path.Value ?? "unknown";
            var requestMethod = context.Request.Method;

            try
            {
                await _next(context);
            }
            finally
            {
                stopwatch.Stop();
                var duration = stopwatch.Elapsed;

                // Log performance metrics
                _logger.LogInformation("Request {Method} {Path} completed in {Duration}ms with status {StatusCode}",
                    requestMethod, requestPath, duration.TotalMilliseconds, context.Response.StatusCode);

                // Track performance metrics
                var operationName = $"{requestMethod}_{requestPath.Replace("/", "_")}";
                performanceService.TrackOperation(operationName, duration);

                // Log slow requests
                if (duration.TotalMilliseconds > 1000)
                {
                    _logger.LogWarning("Slow request detected: {Method} {Path} took {Duration}ms",
                        requestMethod, requestPath, duration.TotalMilliseconds);
                }

                // Add performance headers for debugging
                if (context.Response.HasStarted == false)
                {
                    context.Response.Headers.Add("X-Response-Time", duration.TotalMilliseconds.ToString("F2"));
                }
            }
        }
    }

    public static class PerformanceMonitoringMiddlewareExtensions
    {
        public static IApplicationBuilder UsePerformanceMonitoring(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PerformanceMonitoringMiddleware>();
        }
    }

    // Database query performance interceptor
    public class DatabasePerformanceInterceptor
    {
        private readonly ILogger<DatabasePerformanceInterceptor> _logger;
        private readonly IPerformanceMonitoringService _performanceService;

        public DatabasePerformanceInterceptor(
            ILogger<DatabasePerformanceInterceptor> logger,
            IPerformanceMonitoringService performanceService)
        {
            _logger = logger;
            _performanceService = performanceService;
        }

        public void LogQueryPerformance(string queryType, string tableName, TimeSpan duration)
        {
            _logger.LogDebug("Database query {QueryType} on {TableName} completed in {Duration}ms",
                queryType, tableName, duration.TotalMilliseconds);

            _performanceService.TrackDatabaseQuery($"{queryType}_{tableName}", duration);

            // Log slow queries
            if (duration.TotalMilliseconds > 500)
            {
                _logger.LogWarning("Slow database query detected: {QueryType} on {TableName} took {Duration}ms",
                    queryType, tableName, duration.TotalMilliseconds);
            }
        }
    }

    // File operation performance tracker
    public class FileOperationPerformanceTracker
    {
        private readonly ILogger<FileOperationPerformanceTracker> _logger;
        private readonly IPerformanceMonitoringService _performanceService;

        public FileOperationPerformanceTracker(
            ILogger<FileOperationPerformanceTracker> logger,
            IPerformanceMonitoringService performanceService)
        {
            _logger = logger;
            _performanceService = performanceService;
        }

        public async Task<T> TrackFileOperationAsync<T>(
            string operationType,
            long fileSize,
            Func<Task<T>> operation)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = await operation();
                stopwatch.Stop();

                _performanceService.TrackFileOperation(operationType, fileSize, stopwatch.Elapsed);

                _logger.LogInformation("File operation {OperationType} for {FileSize} bytes completed in {Duration}ms",
                    operationType, fileSize, stopwatch.Elapsed.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "File operation {OperationType} failed after {Duration}ms",
                    operationType, stopwatch.Elapsed.TotalMilliseconds);
                throw;
            }
        }

        public T TrackFileOperation<T>(
            string operationType,
            long fileSize,
            Func<T> operation)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                var result = operation();
                stopwatch.Stop();

                _performanceService.TrackFileOperation(operationType, fileSize, stopwatch.Elapsed);

                _logger.LogInformation("File operation {OperationType} for {FileSize} bytes completed in {Duration}ms",
                    operationType, fileSize, stopwatch.Elapsed.TotalMilliseconds);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "File operation {OperationType} failed after {Duration}ms",
                    operationType, stopwatch.Elapsed.TotalMilliseconds);
                throw;
            }
        }
    }

    // Memory usage monitoring
    public class MemoryMonitoringService
    {
        private readonly ILogger<MemoryMonitoringService> _logger;
        private readonly Timer _timer;

        public MemoryMonitoringService(ILogger<MemoryMonitoringService> logger)
        {
            _logger = logger;
            
            // Monitor memory usage every 5 minutes
            _timer = new Timer(LogMemoryUsage, null, TimeSpan.Zero, TimeSpan.FromMinutes(5));
        }

        private void LogMemoryUsage(object state)
        {
            try
            {
                var process = System.Diagnostics.Process.GetCurrentProcess();
                var workingSet = process.WorkingSet64;
                var privateMemory = process.PrivateMemorySize64;
                
                _logger.LogInformation("Memory usage - Working Set: {WorkingSet:N0} bytes, Private Memory: {PrivateMemory:N0} bytes",
                    workingSet, privateMemory);

                // Log warning if memory usage is high
                var workingSetMB = workingSet / (1024 * 1024);
                if (workingSetMB > 500) // 500MB threshold
                {
                    _logger.LogWarning("High memory usage detected: {WorkingSetMB}MB", workingSetMB);
                }

                // Force garbage collection if memory usage is very high
                if (workingSetMB > 1000) // 1GB threshold
                {
                    _logger.LogWarning("Forcing garbage collection due to high memory usage: {WorkingSetMB}MB", workingSetMB);
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error monitoring memory usage");
            }
        }

        public void Dispose()
        {
            _timer?.Dispose();
        }
    }
}
