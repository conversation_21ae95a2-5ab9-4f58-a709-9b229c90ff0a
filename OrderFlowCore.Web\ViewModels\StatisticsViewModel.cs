using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class StatisticsViewModel
    {
        public GeneralStatisticsDto GeneralStatistics { get; set; } = new GeneralStatisticsDto();
        public StageStatisticsDto StageStatistics { get; set; } = new StageStatisticsDto();
        public List<OrderDepartmentStatisticsDto> DepartmentStatistics { get; set; } = new List<OrderDepartmentStatisticsDto>();
        public List<SupervisorStatisticsDto> SupervisorStatistics { get; set; } = new List<SupervisorStatisticsDto>();
        public List<AssistantManagerStatisticsDto> AssistantManagerStatistics { get; set; } = new List<AssistantManagerStatisticsDto>();
        public List<TransferTypeStatisticsDto> TransferTypeStatistics { get; set; } = new List<TransferTypeStatisticsDto>();
    }
}