using OrderFlowCore.Core.Entities;
using System;

namespace OrderFlowCore.Application.DTOs
{
    public class OrderSummaryDto
    {
        public int Id { get; set; }
        public string EmployeeName { get; set; } = string.Empty;
        public string OrderType { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public OrderStatus OrderStatus { get; set; }
        public string CivilRecord { get; set; } = string.Empty;
    }
}