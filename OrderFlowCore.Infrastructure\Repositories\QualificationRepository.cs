using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure;

namespace OrderFlowCore.Infrastructure.Data
{
    public class QualificationRepository : IQualificationRepository
    {
        private readonly ApplicationDbContext _context;

        public QualificationRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<QualificationDto>> GetAllAsync()
        {
            var qualifications = await _context.Qualifications
                .OrderBy(q => q.Name)
                .ToListAsync();

            return qualifications.Select(MapToDto);
        }

        public async Task<QualificationDto?> GetByIdAsync(int id)
        {
            var qualification = await _context.Qualifications
                .FirstOrDefaultAsync(q => q.Id == id);

            return qualification != null ? MapToDto(qualification) : null;
        }

        public async Task<bool> CreateAsync(QualificationDto qualificationDto)
        {
            var qualification = MapToEntity(qualificationDto);
            await _context.Qualifications.AddAsync(qualification);
            
            return true;
        }

        public async Task<bool> UpdateAsync(QualificationDto qualificationDto)
        {
            var qualification = await _context.Qualifications.FindAsync(qualificationDto.Id);
            if (qualification == null)
                return false;

            qualification.Name = qualificationDto.Name;
            
            
            return true;
        }

        public async Task<bool> DeleteAsync(int id)
        {
            var qualification = await _context.Qualifications.FindAsync(id);
            if (qualification == null)
                return false;

            _context.Qualifications.Remove(qualification);
            
            return true;
        }

        public async Task<bool> ExistsAsync(string name, int? excludeId = null)
        {
            var query = _context.Qualifications.Where(q => q.Name == name);
            
            if (excludeId.HasValue)
            {
                query = query.Where(q => q.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }

        private static QualificationDto MapToDto(Qualification qualification)
        {
            return new QualificationDto
            {
                Id = qualification.Id,
                Name = qualification.Name,
                
            };
        }

        private static Qualification MapToEntity(QualificationDto qualificationDto)
        {
            return new Qualification
            {
                Id = qualificationDto.Id,
                Name = qualificationDto.Name,
               
            };
        }
    }
} 