using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.IO;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IDepartmentService
{
    Task<ServiceResult<IEnumerable<DepartmentDto>>> GetAllDepartmentsAsync();
    Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(int id);
    Task<ServiceResult> CreateDepartmentAsync(DepartmentDto dto);
    Task<ServiceResult> UpdateDepartmentAsync(DepartmentDto dto);
    Task<ServiceResult> DeleteDepartmentAsync(int id);

    // Import/Export functionality
    Task<ServiceResult<byte[]>> ExportToExcelAsync();
    Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream);
}
