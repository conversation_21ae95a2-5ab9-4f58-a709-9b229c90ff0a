/**
 * DirectManager View JavaScript
 * Handles order management functionality for the DirectManager view
 */

$(document).ready(function () {
    // Initialize Bootstrap modals
    const confirmOrderModal = new bootstrap.Modal(document.getElementById('confirmOrderModal'));
    const rejectOrderModal = new bootstrap.Modal(document.getElementById('rejectOrderModal'));

    // Initialize the OrderDetailsModule with configuration
    OrderDetailsModule.init({
        showLoading: function() {
            $('#loading').show();
            $('#orderDetails').hide();
            $('#quickActions').hide();
        },
        hideLoading: function() {
            $('#loading').hide();
        },
        showMessage: function(message, type) {
            const alertClass = type === 'error' ? 'alert-danger' : 'alert-success';
            const icon = type === 'error' ? '❌' : '✅';
            $('#messageContainer').html(`<div class="alert ${alertClass} fade-in">${icon} ${message}</div>`);
            
            // Auto-hide success messages after 5 seconds
            if (type === 'success') {
                setTimeout(() => {
                    $('#messageContainer .alert').fadeOut();
                }, 5000);
            }
        },
        showOrderDetails: function() {
            $('#orderDetails').show();
            $('#quickActions').show();
            $('#orderDetails').addClass('fade-in');
        },
        hideOrderDetails: function() {
            $('#orderDetails').hide();
            $('#quickActions').hide();
        }
    });

    // Order selection change handler
    $('#orderSelect').change(function () {
        const orderId = $(this).val();
        if (orderId) {
            OrderDetailsModule.loadOrderDetails(orderId, '/DirectManager/GetOrderDetails');
        } else {
            OrderDetailsModule.hideOrderDetails();
        }
    });


    // Confirm order button handler
    $('#confirmOrderBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.show();
        }
    });

    // Reject order button handler
    $('#rejectOrderBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            const reason = $('#rejectReason').val();
            if (reason.trim()) {
                $('#rejectReasonModal').val(reason);
                rejectOrderModal.show();
            } else {
                OrderDetailsModule.showMessage('يجب إدخال سبب الإلغاء', 'error');
                $('#rejectReason').focus();
            }
        }
    });

    // Download attachments button handler
    $('#downloadAttachmentsBtn').click(function () {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            downloadAttachmentsWithoutRefresh(currentOrderId);
        }
    });

    // Function to download attachments without page refresh
    function downloadAttachmentsWithoutRefresh(orderId) {
        // Show loading state
        const $btn = $('#downloadAttachmentsBtn');
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>جاري التحميل...');

        // Create a temporary link element for download
        const downloadUrl = `/DirectManager/DownloadAttachments/${orderId}`;

        // Use fetch to handle the download
        fetch(downloadUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.blob();
        })
        .then(blob => {
            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `مرفقات_طلب_${orderId}.zip`;

            // Trigger download
            document.body.appendChild(a);
            a.click();

            // Cleanup
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // Show success message
            OrderDetailsModule.showMessage('تم تحميل المرفقات بنجاح', 'success');
        })
        .catch(error => {
            console.error('Download error:', error);
            OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل المرفقات', 'error');
        })
        .finally(() => {
            // Restore button state
            $btn.prop('disabled', false).html(originalText);
        });
    }

    // Modal confirmation button handlers
    $('#confirmOrderModalBtn').click(function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            confirmOrderModal.hide();

            const formData = new FormData();
            formData.append('orderId', currentOrderId);

            $.ajax({
                url: '/DirectManager/ConfirmOrder',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (data) => {
                    if (data.success) {
                        OrderDetailsModule.showMessage(data.message || 'تم تأكيد الطلب بنجاح', 'success');
                        setTimeout(() => {
                            window.location.href = '/DirectManager';
                        }, 2000);
                    } else {
                        OrderDetailsModule.showMessage(data.message || 'حدث خطأ أثناء تأكيد الطلب', 'error');
                    }
                },
                error: (xhr, status, error) => {
                    OrderDetailsModule.showMessage('حدث خطأ أثناء تحويل الطلب', 'error');
                    console.error('Error:', error);
                }
            });
        }
    });

    $('#rejectOrderModalBtn').click(function() {
        const currentOrderId = OrderDetailsModule.getCurrentOrderId();
        if (currentOrderId) {
            const reason = $('#rejectReason').val();
            rejectOrderModal.hide();

            const formData = new FormData();
            formData.append('orderId', currentOrderId);
            formData.append('reason', reason);
            $.ajax({
                url: '/DirectManager/RejectOrder',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: (data) => {
                    if (data.success) {
                        OrderDetailsModule.showMessage(data.message || 'تم رفض الطلب بنجاح', 'success');
                        setTimeout(() => {
                            window.location.href = '/DirectManager';
                        }, 2000);
                    } else {
                        OrderDetailsModule.showMessage(data.message || 'حدث خطأ أثناء رفض الطلب', 'error');
                    }
                },
                error: (xhr, status, error) => {
                    OrderDetailsModule.showMessage('حدث خطأ أثناء رفض الطلب', 'error');
                    console.error('Error:', error);
                }
            });
        }
    });
}); 