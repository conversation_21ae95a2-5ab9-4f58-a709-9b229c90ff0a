﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Helper
{
    public static class OrderHelper
    {
        public static string ConfirmedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | اعتماد بواسطة: {username}";

        public static string RejectedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تم الالغاء بواسطة: {username}";

        public static string RestoredBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | استعادة بواسطة: {username}";

        public static string ReturnedBy(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تمت الإعادة بواسطة: {username}";

        public static string OrderUnderImplementation() => $"{DateTime.Now:yyyy-MM-dd} | الطلب قيد التنفيذ";

        public static string OrderNeedActionByCoordinator(string username)
            => $"{DateTime.Now:yyyy-MM-dd} | طلب إجراء من المنسق: {username}";
        public static string OrderNeedActionBySupervisor(string username)
            => $"{DateTime.Now:yyyy-MM-dd} | طلب إجراء من المشرف: {username}";

        public static string OrderDirectToManager(string username)
            => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل مباشر للمدير: {username}";

        // New methods for tracking assignment dates
        public static string AssignedToDirectManager(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل لمدير القسم: {username}";

        public static string AssignedToAssistantManager(string? username) => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل لمساعد المدير: {username}";

        public static string AssignedToCoordinator() => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل للمنسق";

        public static string AssignedToHRManager() => $"{DateTime.Now:yyyy-MM-dd} | تم التحويل لمدير الموارد البشرية";

        // Methods for tracking both assignment and completion
        public static string ConfirmedByWithAssignment(string? username, string assignmentDate) => $"{assignmentDate} | اعتماد بواسطة: {username} | {DateTime.Now:yyyy-MM-dd}";

        public static string RejectedByWithAssignment(string? username, string assignmentDate) => $"{assignmentDate} | تم الالغاء بواسطة: {username} | {DateTime.Now:yyyy-MM-dd}";

        public static string ReturnedByWithAssignment(string? username, string assignmentDate) => $"{assignmentDate} | تمت الإعادة بواسطة: {username} | {DateTime.Now:yyyy-MM-dd}";

        public static string OrderDirectToManagerWithAssignment(string? username, string assignmentDate) => $"{assignmentDate} | تم التحويل مباشر للمدير: {username} | {DateTime.Now:yyyy-MM-dd}";

        public static string OrderNeedActionByCoordinatorWithAssignment(string? username, string assignmentDate) => $"{assignmentDate} | طلب إجراء من المنسق: {username} | {DateTime.Now:yyyy-MM-dd}";

        // Helper method to extract outDate (completion date) from status string
        public static string ExtractOutDate(string status)
        {
            if (string.IsNullOrEmpty(status))
                return "-";

            var parts = status.Split(" | ");
            if (parts.Length >= 3)
            {
                // Format: "assignmentDate | action | outDate"
                return parts[2];
            }
            else if (parts.Length >= 2)
            {
                // Format: "date | action" (old format)
                return parts[0];
            }
            
            return status;
        }

        public static string ExtractInDate(string status)
        {
            if (string.IsNullOrEmpty(status))
                return "-";

            var parts = status.Split(" | ");

            return parts[0];
        }
    }

    public static class OrderTransferTypes
    {
        public const string Manually = "يدوي";
        public const string Auto = "تلقائي";
        public const string Direct = "سريع";
        public const string DirectToManager = "مباشر للمدير";
    }
}
