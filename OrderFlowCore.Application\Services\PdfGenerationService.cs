﻿using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using FontSize = DocumentFormat.OpenXml.Wordprocessing.FontSize;
using Paragraph = DocumentFormat.OpenXml.Wordprocessing.Paragraph;
using Run = DocumentFormat.OpenXml.Wordprocessing.Run;
using RunProperties = DocumentFormat.OpenXml.Wordprocessing.RunProperties;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;

namespace OrderFlowCore.Application.Services;
public class PdfGenerationService
{
    private readonly string _templatePath;
    private readonly Dictionary<string, Func<OrderDetailsDto, string>> _bookmarkMappings;
    private readonly IEnvironmentService _env;
    private byte[] _cachedTemplate;
    private DateTime _templateLastModified;

    public PdfGenerationService(IEnvironmentService env)
    {
        _templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Resources", "Templates", "Form.docx");
        _bookmarkMappings = InitializeBookmarkMappings();
        _env = env;
    }

    public async Task<ServiceResult<byte[]>> GenerateOrderPdfAsync(OrderDetailsDto order)
    {
        try
        {
            if (order == null)
                return ServiceResult<byte[]>.Failure("Order data is required");

            // return file if it already exists
            var existingFilePath = Path.Combine(GetPrintedDirectory(), $"{order.Id}.pdf");
            if (File.Exists(existingFilePath))
            {
                return ServiceResult<byte[]>.Success(File.ReadAllBytes(existingFilePath), "PDF already exists");
            }

            // Get template with caching
            var templateBytes = await GetTemplateAsync();
            if (templateBytes == null)
                return ServiceResult<byte[]>.Failure("Template file not found");

            // Process in memory without unnecessary copies
            using var outputStream = new MemoryStream();

            // Process Word document
            using (var templateStream = new MemoryStream(templateBytes))
            using (var wordDoc = WordprocessingDocument.Open(templateStream, true))
            {
                ProcessBookmarks(wordDoc, order);
                wordDoc.Save();

                // Convert to PDF directly
                templateStream.Position = 0;
                return await ConvertToPdfAsync(templateStream, Path.Combine(GetPrintedDirectory(), $"{order.Id}.pdf"));
            }
        }
        catch (Exception ex)
        {
            return ServiceResult<byte[]>.Failure($"Error generating PDF: {ex.Message}");
        }
    }

    private async Task<byte[]> GetTemplateAsync()
    {
        if (!File.Exists(_templatePath))
            return null;

        var fileInfo = new FileInfo(_templatePath);

        // Cache template if not cached or file has been modified
        if (_cachedTemplate == null || fileInfo.LastWriteTime > _templateLastModified)
        {
            _cachedTemplate = await File.ReadAllBytesAsync(_templatePath);
            _templateLastModified = fileInfo.LastWriteTime;
        }

        // Return a copy to avoid modifying the cached version
        return _cachedTemplate.ToArray();
    }

    private void ProcessBookmarks(WordprocessingDocument wordDoc, OrderDetailsDto order)
    {
        var bookmarks = wordDoc.MainDocumentPart.RootElement
            .Descendants<BookmarkStart>()
            .Where(b => _bookmarkMappings.ContainsKey(b.Name.Value))
            .ToList();

        foreach (var bookmark in bookmarks)
        {
            var value = _bookmarkMappings[bookmark.Name.Value](order);
            if (!string.IsNullOrWhiteSpace(value))
            {
                InsertTextAtBookmark(bookmark, value);
            }
        }
    }

    private async Task<ServiceResult<byte[]>> ConvertToPdfAsync(MemoryStream wordStream,string filePath)
    {
        return await Task.Run(() =>
        {
            using var spireDoc = new Spire.Doc.Document();
            spireDoc.LoadFromStream(wordStream, Spire.Doc.FileFormat.Docx);
            spireDoc.SaveToFile(filePath, Spire.Doc.FileFormat.PDF);

            return ServiceResult<byte[]>.Success(File.ReadAllBytes(filePath));
        });
    }

    private void InsertTextAtBookmark(BookmarkStart bookmark, string text)
    {
        var parts = text.Split('|', StringSplitOptions.TrimEntries);
        if( parts.Length >= 3)
        {
            // Insert name
            var nameRun = CreateRun(parts[1]);
            var nameParagraph = new Paragraph(nameRun);
            bookmark.Parent.InsertAfter(nameParagraph, bookmark);
            // Insert date with smaller font
            var dateRun = CreateRun(parts[2], 18);
            var dateParagraph = new Paragraph(dateRun);
            bookmark.Parent.InsertAfter(dateParagraph, nameParagraph);
        }
        else if (parts.Length >= 2)
        {
            // Insert name
            var nameRun = CreateRun(parts[1]);
            var nameParagraph = new Paragraph(nameRun);
            bookmark.Parent.InsertAfter(nameParagraph, bookmark);

            // Insert date with smaller font
            var dateRun = CreateRun(parts[0], 18);
            var dateParagraph = new Paragraph(dateRun);
            bookmark.Parent.InsertAfter(dateParagraph, nameParagraph);
        }
        else
        {
            // Insert single text
            var run = CreateRun(text);
            bookmark.Parent.InsertAfter(run, bookmark);
        }
    }

    private Run CreateRun(string text, int? fontSize = null)
    {
        var run = new Run(new Text(text));

        if (fontSize.HasValue)
        {
            run.RunProperties = new RunProperties
            {
                FontSize = new FontSize { Val = fontSize.Value.ToString() }
            };
        }

        return run;
    }

    private Dictionary<string, Func<OrderDetailsDto, string>> InitializeBookmarkMappings()
    {
        return new Dictionary<string, Func<OrderDetailsDto, string>>
        {
            ["bm1"] = o => o.EmployeeName,
            ["bm2"] = o => o.MobileNumber,
            ["bm3"] = o => o.EmployeeNumber,
            ["bm4"] = o => o.CivilRecord,
            ["bm5"] = o => o.EmploymentType,
            ["bm6"] = o => o.Nationality,
            ["bm7"] = o => o.Department,
            ["bm8"] = o => o.Details,
            ["bm9"] = o => o.ConfirmedByAssistantManager,
            ["bm10"] = o => o.CoordinatorDetails,
            ["bm11"] = o => o.ConfirmedByDepartmentManager,
            ["bm12"] = o => o.SupervisorOfEmployeeServices,
            ["bm13"] = o => o.SupervisorOfLegalAndCompliance,
            ["bm14"] = o => o.SupervisorOfOutpatientClinics,
            ["bm15"] = o => o.SupervisorOfSecurity,
            ["bm16"] = o => o.SupervisorOfHumanResourcesPlanning,
            ["bm17"] = o => o.SupervisorOfPayrollAndBenefits,
            ["bm18"] = o => o.SupervisorOfFiles,
            ["bm19"] = o => o.SupervisorOfRevenueDevelopment,
            ["bm20"] = o => o.SupervisorOfInformationTechnology,
            ["bm21"] = o => o.SupervisorOfMedicalRecords,
            ["bm22"] = o => o.SupervisorOfHousing,
            ["bm23"] = o => o.SupervisorOfInventoryControl,
            ["bm24"] = o => o.SupervisorOfAttendance,
            ["bm25"] = o => o.SupervisorOfHumanResourcesServices,
            ["bm26"] = o => o.SupervisorOfSocialSecurity,
            ["bm27"] = o => o.SupervisorOfMedicalConsultation,
            ["bm28"] = o => o.HumanResourcesManager,
            ["bm29"] = o => o.ConfirmedByCoordinator,
            ["bm30"] = o => DateTime.Now.ToString("yyyy/MM/dd"),
            ["bm31"] = o => o.JobTitle,
            ["bm32"] = o => o.OrderType,
            ["bm33"] = o => o.Id.ToString(),
            ["bm34"] = o => o.Qualification
        };
    }

    private string GetPrintedDirectory()
    {
        var uploadDir = Path.Combine(_env.WebRootPath, "order_printed");

        Directory.CreateDirectory(uploadDir);
        return uploadDir;
    }
}

