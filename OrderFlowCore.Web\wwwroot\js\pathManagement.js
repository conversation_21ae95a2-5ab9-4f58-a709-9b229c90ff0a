// JavaScript for PathManagement/Index.cshtml
// Moved from Razor view

// Enhanced interactive functionality
$(document).on('click', '.supervisor-card', function (e) {
    if (!$(e.target).is('input[type="checkbox"]')) {
        $(this).find('input[type="checkbox"]').trigger('click');
    }
});

$(document).on('change', '.supervisor-card input[type="checkbox"]', function () {
    $(this).closest('.supervisor-card').toggleClass('selected', this.checked);
});


function showAlert(type, message) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
    $('.container-fluid').prepend(alertHtml);
    setTimeout(function () {
        $('.alert').alert('close');
    }, 5000);
}

// Show loading state on form submission
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function (e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<span class="spinner-custom me-2"></span>جاري الحفظ...';
        submitBtn.disabled = true;
    });
});


// Save all manual paths on button click
$(document).on('click', '.btn-save-manual-paths', function (e) {
    e.preventDefault();
    const $btn = $(this);
    $btn.prop('disabled', true).html('<span class="spinner-custom me-2"></span>جاري الحفظ...');
    // Collect mapping
    const paths = {};
    $('.form-check-input[data-path][data-supervisor]:checked').each(function () {
        const path = $(this).data('path');
        const supervisor = $(this).data('supervisor');
        if (!paths[path]) paths[path] = [];
        paths[path].push(supervisor);
    });
    $.ajax({
        url: window.saveManualPathsUrl,
        type: 'POST',
        contentType: 'application/json',
        headers: { 'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val() },
        data: JSON.stringify(paths),
        success: function (response) {
            if (response.success) {
                showAlert('success', response.message);
                // Reload the manual tab and update cache
                $.get(tabUrls['manual'], function (html) {
                    tabCache['manual'] = html;
                    $('#tabContentArea').html(html);
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            } else {
                showAlert('error', response.message);
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        },
        error: function () {
            showAlert('error', 'حدث خطأ أثناء حفظ التغييرات.');
        },
        complete: function () {
            $btn.prop('disabled', false).html('<i class="fas fa-save me-2"></i>حفظ التغييرات');
        }
    });
});


// save for auto route form
$(document).on('submit', '.auto-route-form', function (e) {
    e.preventDefault();
    var $form = $(this);
    var formData = $form.serialize();
    var $btn = $form.find('button[type="submit"]');
    var originalText = $btn.html();
    $btn.html('<span class="spinner-custom me-2"></span>جاري الحفظ...').prop('disabled', true);
    $.ajax({
        url: window.saveAutoRouteUrl,
        type: 'POST',
        data: formData,
        success: function (response) {
            if (response.success) {
                showAlert('success', response.message);
                // Reload the auto tab and update cache
                $.get(tabUrls['auto'], function (html) {
                    tabCache['auto'] = html;
                    $('#tabContentArea').html(html);
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            } else {
                showAlert('error', response.message || 'حدث خطأ أثناء حفظ المسار التلقائي.');
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        },
        error: function () {
            showAlert('error', 'حدث خطأ أثناء حفظ المسار التلقائي.');
        },
        complete: function () {
            $btn.html(originalText).prop('disabled', false);
        }
    });
});

// Edit Auto Route
$(document).on('click', '.btn-edit-auto', function () {
    var routeId = $(this).data('id');
    $.get(window.getAutoRouteUrl + routeId, function (response) {
        if (response.success) {
            const data = response.data;
            // Populate form fields
            $('select[name="OrderType"]').val(data.orderType);
            $('select[name="Nationality"]').val(data.nationality);
            $('select[name="Job"]').val(data.job);
            $('input[name="Status"]').prop('checked', data.status);
            // Supervisors
            $('input[name="SelectedSupervisors"]').prop('checked', false);
            $('.supervisor-card').removeClass('selected'); // Clear previous selections
            data.supervisors.forEach(function (sup) {
                $('input[name="SelectedSupervisors"][value="' + sup + '"]').prop('checked', true);
                $('.supervisor-card input[type="checkbox"][value="' + sup + '"]').closest('.supervisor-card').addClass('selected');
            });
            // Set or update hidden Id field
            
            $('#autoRouteId').val(data.id);
            
            // Change form title and button text
            $('#autoRouteFormTitle').text('تعديل المسار التلقائي');
            $('#autoRouteSubmitBtn span').text('تحديث المسار');
        } else {
            showAlert('error', response.message);
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    });
});

// Reset Auto Route Form
$(document).on('reset', '.auto-route-form', function () {
    // Clear selects and checkboxes
    $(this).find('select').val('');
    $(this).find('input[type="checkbox"]').prop('checked', false);
    // Clear supervisor-card selected
    $(this).find('.supervisor-card').removeClass('selected');
    $('#autoRouteId').val('');
    // Reset form title and button text
    $('#autoRouteFormTitle').text('إضافة مسار تلقائي جديد');
    $('#autoRouteSubmitBtn span').text('حفظ المسار');
});

let deleteRouteId = null;
let deleteRouteType = null; // 'auto' or 'direct'

$(document).on('click', '.btn-delete-auto', function () {
    deleteRouteId = $(this).data('id');
    deleteRouteType = 'auto';
    $('#deleteConfirmModal').modal('show');
});

$(document).on('click', '.btn-delete-direct', function () {
    deleteRouteId = $(this).data('id');
    deleteRouteType = 'direct';
    $('#deleteConfirmModal').modal('show');
});

$('#confirmDeleteBtn').on('click', function () {
    if (!deleteRouteId || !deleteRouteType) return;
    let url = '';
    if (deleteRouteType === 'auto') {
        url = window.deleteAutoRouteUrl;
    } else if (deleteRouteType === 'direct') {
        url = window.deleteDirectRouteUrl;
    }
    $.post(url, { id: deleteRouteId, __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val() }, function (response) {
        if (response.success) {
            showAlert('success', response.message);
            // Reload the relevant tab and update cache
            const tabToReload = deleteRouteType;
            $.get(tabUrls[tabToReload], function (html) {
                tabCache[tabToReload] = html;
                $('#tabContentArea').html(html);
            });
            
            window.scrollTo({ top: 0, behavior: 'smooth' });
        } else {
            showAlert('error', response.message);
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        $('#deleteConfirmModal').modal('hide');
        deleteRouteId = null;
        deleteRouteType = null;
    });
});

// Save Direct Route
$(document).on('submit', '.direct-route-form', function (e) {
    e.preventDefault();
    var $form = $(this);
    var formData = $form.serialize();
    var $btn = $form.find('button[type="submit"]');
    var originalText = $btn.html();
    $btn.html('<span class="spinner-custom me-2"></span>جاري الحفظ...').prop('disabled', true);

    $.ajax({
        url: window.saveDirectRouteUrl,
        type: 'POST',
        data: formData,
        success: function (response) {
            if (response.success) {
                showAlert('success', response.message);
                // Reload the direct tab and update cache
                $.get(tabUrls['direct'], function (html) {
                    tabCache['direct'] = html;
                    $('#tabContentArea').html(html);
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                });
            } else {
                showAlert('error', response.message || 'حدث خطأ أثناء حفظ المسار السريع.');
                window.scrollTo({ top: 0, behavior: 'smooth' });
            }
        },
        error: function () {
            showAlert('error', 'حدث خطأ أثناء حفظ المسار السريع.');
        },
        complete: function () {
            $btn.html(originalText).prop('disabled', false);
        }
    });
});

// Edit Direct Route
$(document).on('click', '.btn-edit-direct', function () {
    var routeId = $(this).data('id');
    $.get(window.getDirectRouteUrl + routeId, function (response) {
        if (response.success) {
            const data = response.data;
            // Populate form fields
            $('select[name="OrderType"]').val(data.orderType);
            $('select[name="Nationality"]').val(data.nationality);
            $('select[name="Job"]').val(data.job);
            $('input[name="Status"]').prop('checked', data.status);
            // Supervisors
            $('input[name="SelectedSupervisors"]').prop('checked', false);
            $('.supervisor-card').removeClass('selected'); // Clear previous selections
            data.supervisors.forEach(function (sup) {
                $('input[name="SelectedSupervisors"][value="' + sup + '"]').prop('checked', true);
                $('.supervisor-card input[type="checkbox"][value="' + sup + '"]').closest('.supervisor-card').addClass('selected');
            });
            
            $('#directRouteId').val(data.id);
            // Change button text
            $('#directRouteFormTitle').text('تعديل المسار السريع');
            $('form[asp-action="SaveDirectRoute"] button[type="submit"]').text('تحديث المسار السريع');
        } else {
            showAlert('error', response.message);
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    });
});

// Reset Direct Route Form
$(document).on('reset', '.direct-route-form', function () {
    // Clear selects and checkboxes
    $(this).find('select').val('');
    $(this).find('input[type="checkbox"]').prop('checked', false);
    // Clear supervisor-card selected
    $(this).find('.supervisor-card').removeClass('selected');
    // Reset form title and button text
    $('#directRouteId').val('');
    $('#directRouteFormTitle').text('إضافة مسار سريع جديد');
    $('#btnSaveDirectRoute').text('حفظ المسار السريع');
});



// AJAX tab loading
const tabUrls = window.pathTabs;
const tabCache = {};

function loadTab(tab) {
    $('#tabContentArea').html('<div class="text-center py-5"><span class="spinner-border"></span></div>');
    if (tabCache[tab]) {
        $('#tabContentArea').html(tabCache[tab]);
        return;
    }
    $.get(tabUrls[tab], function (html) {
        tabCache[tab] = html;
        $('#tabContentArea').html(html);
    });
}

$(document).ready(function () {
    loadTab('manual');
    $('.nav-tabs-custom .nav-link').on('click', function (e) {
        e.preventDefault();
        $('.nav-tabs-custom .nav-link').removeClass('active');
        $(this).addClass('active');
        const tab = $(this).attr('href').replace('#', '').replace('-tab', '');
        loadTab(tab);
    });
}); 