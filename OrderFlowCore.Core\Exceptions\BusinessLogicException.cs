namespace OrderFlowCore.Core.Exceptions
{
    public class BusinessLogicException : Exception
    {
        public string ErrorCode { get; }
        public object? Details { get; protected set; }

        public BusinessLogicException(string message) : base(message)
        {
            ErrorCode = "BUSINESS_LOGIC_ERROR";
        }

        public BusinessLogicException(string message, string errorCode) : base(message)
        {
            ErrorCode = errorCode;
        }

        public BusinessLogicException(string message, string errorCode, object? details) : base(message)
        {
            ErrorCode = errorCode;
            Details = details;
        }

        public BusinessLogicException(string message, Exception innerException) : base(message, innerException)
        {
            ErrorCode = "BUSINESS_LOGIC_ERROR";
        }

        public BusinessLogicException(string message, string errorCode, Exception innerException) : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }

    public class ValidationException : BusinessLogicException
    {
        public List<string> ValidationErrors { get; }

        public ValidationException(string message) : base(message, "VALIDATION_ERROR")
        {
            ValidationErrors = new List<string> { message };
        }

        public ValidationException(List<string> validationErrors) : base("Validation failed", "VALIDATION_ERROR")
        {
            ValidationErrors = validationErrors;
        }

        public ValidationException(string message, List<string> validationErrors) : base(message, "VALIDATION_ERROR")
        {
            ValidationErrors = validationErrors;
        }
    }

    public class OrderNotFoundException : BusinessLogicException
    {
        public OrderNotFoundException(int orderId) : base($"Order with ID {orderId} was not found", "ORDER_NOT_FOUND")
        {
            this.Details = new { OrderId = orderId };
        }

        public OrderNotFoundException(string orderNumber) : base($"Order with number {orderNumber} was not found", "ORDER_NOT_FOUND")
        {
            Details = new { OrderNumber = orderNumber };
        }
    }

    public class FileProcessingException : BusinessLogicException
    {
        public FileProcessingException(string message) : base(message, "FILE_PROCESSING_ERROR")
        {
        }

        public FileProcessingException(string message, Exception innerException) : base(message, "FILE_PROCESSING_ERROR", innerException)
        {
        }
    }

    public class UnauthorizedOperationException : BusinessLogicException
    {
        public UnauthorizedOperationException(string operation) : base($"Unauthorized to perform operation: {operation}", "UNAUTHORIZED_OPERATION")
        {
            Details = new { Operation = operation };
        }

        public UnauthorizedOperationException(string operation, string userId) : base($"User {userId} is unauthorized to perform operation: {operation}", "UNAUTHORIZED_OPERATION")
        {
            Details = new { Operation = operation, UserId = userId };
        }
    }
}
