using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IDashboardService
    {
        Task<ServiceResult<DashboardStatisticsDto>> GetDashboardStatisticsAsync();
        Task<ServiceResult<HRCoordinatorStatisticsDto>> GetHRCoordinatorStatisticsAsync();
    }

    public class DashboardStatisticsDto
    {
        public OrderStatisticsDto OrderStatistics { get; set; } = new();
        public DepartmentStatisticsDto DepartmentStatistics { get; set; } = new();
        public EmployeeStatisticsDto EmployeeStatistics { get; set; } = new();
        public WorkflowStatisticsDto WorkflowStatistics { get; set; } = new();
    }

    public class OrderStatisticsDto
    {
        public int TotalOrders { get; set; }
        public int PendingOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int CancelledOrders { get; set; }
        public int TodayOrders { get; set; }
        public int ThisWeekOrders { get; set; }
        public int ThisMonthOrders { get; set; }
        public List<OrderStatusCountDto> OrdersByStatus { get; set; } = new();
        public List<OrderTypeCountDto> OrdersByType { get; set; } = new();
        public List<DepartmentOrderCountDto> OrdersByDepartment { get; set; } = new();
    }

    public class DepartmentStatisticsDto
    {
        public int TotalDepartments { get; set; }
        public int AssignedDepartments { get; set; }
        public int UnassignedDepartments { get; set; }
        public List<ManagerDepartmentCountDto> DepartmentsByManager { get; set; } = new();
    }

    public class EmployeeStatisticsDto
    {
        public int TotalEmployees { get; set; }
        public List<NationalityCountDto> EmployeesByNationality { get; set; } = new();
        public List<EmploymentTypeCountDto> EmployeesByType { get; set; } = new();
        public List<QualificationCountDto> EmployeesByQualification { get; set; } = new();
    }



    public class WorkflowStatisticsDto
    {
        public int OrdersAtDirectManager { get; set; }
        public int OrdersAtAssistantManagers { get; set; }
        public int OrdersAtCoordinators { get; set; }
        public int OrdersAtSupervisors { get; set; }
        public int OrdersAtManagers { get; set; }
        public int OrdersRequiringAction { get; set; }
        public List<WorkflowStageCountDto> OrdersByWorkflowStage { get; set; } = new();
    }

    // Supporting DTOs
    public class OrderStatusCountDto
    {
        public OrderStatus Status { get; set; }
        public string StatusDisplayName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string Color { get; set; } = "";
    }

    public class OrderTypeCountDto
    {
        public string OrderType { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class DepartmentOrderCountDto
    {
        public string DepartmentName { get; set; } = "";
        public int Count { get; set; }
        public int PendingCount { get; set; }
        public int CompletedCount { get; set; }
        public double Percentage { get; set; }

    }

    public class ManagerDepartmentCountDto
    {
        public AssistantManagerType ManagerType { get; set; }
        public string ManagerName { get; set; } = "";
        public int DepartmentCount { get; set; }
        public double Percentage { get; set; }
    }

    public class NationalityCountDto
    {
        public string Nationality { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class EmploymentTypeCountDto
    {
        public string EmploymentType { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }

    public class QualificationCountDto
    {
        public string Qualification { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
    }



    public class WorkflowStageCountDto
    {
        public string StageName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string StageColor { get; set; } = "";
    }

    // HR Coordinator specific DTOs
    public class HRCoordinatorStatisticsDto
    {
        // Coordinator-specific order statistics
        public int TotalOrdersForCoordinator { get; set; }
        public int PendingOrdersForCoordinator { get; set; }
        public int ProcessedOrdersToday { get; set; }
        public int ProcessedOrdersThisWeek { get; set; }
        public int ProcessedOrdersThisMonth { get; set; }
        public int OrdersRequiringAction { get; set; }
        public int OrdersAtSupervisors { get; set; }

        public List<DepartmentOrderCountDto> TopDepartments { get; set; } = new();
        
        // Recent activity
        public List<RecentActivityDto> RecentActivities { get; set; } = new();
    }

    public class CoordinatorWorkflowStageDto
    {
        public string StageName { get; set; } = "";
        public int Count { get; set; }
        public double Percentage { get; set; }
        public string StageColor { get; set; } = "";
        public string Description { get; set; } = "";
    }

    public class RecentActivityDto
    {
        public int OrderId { get; set; }
        public string EmployeeName { get; set; } = "";
        public string Department { get; set; } = "";
        public string Action { get; set; } = "";
        public string ActionDescription { get; set; } = "";
        public DateTime Timestamp { get; set; }
        public string Status { get; set; } = "";
        public string Icon { get; set; } = "";
        public string Color { get; set; } = "";
    }
}
