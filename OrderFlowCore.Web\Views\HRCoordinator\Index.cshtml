@model OrderFlowCore.Web.ViewModels.HRCoordinatorViewModel
@{
    ViewData["Title"] = "منسق الموارد البشرية";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">منسق الموارد البشرية</h2>

            <!-- Order Selection Section -->
            <div class="order-select-container mb-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="orderSelect" class="form-label text-white">📋 رقم الطلب:</label>
                        @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                            new { @class = "form-select", @id = "orderSelect"})
                    </div>
                    <div class="col-md-6">
                        <div class="text-center">
                            <p class="mb-0">اختر طلباً لعرض تفاصيله واتخاذ الإجراء المناسب</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Cards -->
            <div class="row mb-4">
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">📝 معالجة الطلبات</h5>
                            <p class="card-text">معالجة الطلبات المحددة وإرسالها للمشرفين</p>
                            <button type="button" class="btn btn-primary" id="btnProcessOrder" disabled>
                                معالجة الطلب
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">🔄 استعادة الطلبات</h5>
                            <p class="card-text">استعادة الطلبات الملغية أو المرفوضة</p>
                            <a href="@Url.Action("Restore", "HRCoordinator")" class="btn btn-info">
                                استعادة الطلبات
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">📊 لوحة الإحصائيات</h5>
                            <p class="card-text">عرض إحصائيات الطلبات والحالة</p>
                            <a href="@Url.Action("Dashboard", "HRCoordinator")" class="btn btn-success">
                                عرض الإحصائيات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div id="messageContainer" class="mt-3"></div>
            <div id="actionRequireMessageContainer" class="mt-3"></div>
            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>

            <!-- Order Details Section -->
            @await Html.PartialAsync("_OrderDetailsPartial")
                    
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrCoordinator.js"></script>
}