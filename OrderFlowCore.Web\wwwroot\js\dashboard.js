﻿// Dashboard JavaScript Module
(function () {
    'use strict';


    // State
    let sidebarCollapsed = false;
    let darkMode = localStorage.getItem('darkMode') === 'enabled';

    // Initialize on DOM ready
    $(document).ready(function () {
        initializeNotifications();
        initializeBootstrapComponents();
        initializeSidebar();
        initializeFullscreen();
        initializeDarkMode();
        fixInitialLayout();
    });

    
    // Initialize Notifications
    function initializeNotifications() {
        loadNotifications();
        setInterval(loadNotifications, 15000);
    }

    function loadNotifications() {
        if (typeof fetchUnreadNotifications === 'function') {
            fetchUnreadNotifications();
        }
    }

    // Initialize Bootstrap Components
    function initializeBootstrapComponents() {
        // Tooltips
        const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipTriggerList.forEach(el => new bootstrap.Tooltip(el));

        // Popovers
        const popoverTriggerList = document.querySelectorAll('[data-bs-toggle="popover"]');
        popoverTriggerList.forEach(el => new bootstrap.Popover(el));

        // Dropdowns
        const dropdownElementList = document.querySelectorAll('.dropdown-toggle');
        dropdownElementList.forEach(el => new bootstrap.Dropdown(el, { autoClose: true }));
    }

    // Initialize Sidebar Toggle
    function initializeSidebar() {
        $('[data-widget="pushmenu"]').on('click', function (e) {
            e.preventDefault();
            toggleSidebar();
        });
    }

    function toggleSidebar() {
        sidebarCollapsed = !sidebarCollapsed;
        $('body').toggleClass('sidebar-collapse', sidebarCollapsed);

        const sidebarPosition = sidebarCollapsed ? '-250px' : '0';
        const contentMargin = sidebarCollapsed ? '0' : '250px';

        $('.app-sidebar').css({
            'right': sidebarPosition,
            'transition': 'right 0.3s ease'
        });

        $('.content-wrapper, .main-header').css({
            'margin-right': contentMargin,
            'transition': 'margin-right 0.3s ease'
        });
    }

    // Initialize Fullscreen
    function initializeFullscreen() {
        $('[data-widget="fullscreen"]').on('click', toggleFullscreen);
    }

    function toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.warn('Error attempting to enable fullscreen:', err);
            });
        } else if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }

    // Initialize Dark Mode
    function initializeDarkMode() {
        applyDarkMode(darkMode);
        $('#darkModeToggle').on('click', toggleDarkMode);
    }

    function toggleDarkMode() {
        darkMode = !darkMode;
        localStorage.setItem('darkMode', darkMode ? 'enabled' : 'disabled');
        applyDarkMode(darkMode);
    }

    function applyDarkMode(enabled) {
        const $body = $('body');
        const $icon = $('#darkModeToggle i');

        if (enabled) {
            $body.addClass('dark-mode');
            $icon.removeClass('fa-moon').addClass('fa-sun');
        } else {
            $body.removeClass('dark-mode');
            $icon.removeClass('fa-sun').addClass('fa-moon');
        }
    }

    // Fix Initial Layout
    function fixInitialLayout() {
        const mainHeader = document.querySelector(".main-header");
        if (mainHeader) {
            mainHeader.style.marginRight = "250px";
        }
    }

    // Export functions for external use if needed
    window.dashboardModule = {
        loadNotifications: loadNotifications,
        toggleSidebar: toggleSidebar,
        toggleFullscreen: toggleFullscreen,
        toggleDarkMode: toggleDarkMode
    };

})();