﻿function fetchUnreadNotifications() {
    $.ajax({
        url: '/Notification/GetUnreadNotifications',
        type: 'GET',
        dataType: 'json',
        success: function (response) {
            var $dropdown = $('#notification-list');
            var $badge = $('#notification-badge');

            $dropdown.empty();

            // Check if response has success property or if it's the direct response from our controller
            var isSuccess = response.success !== undefined ? response.success : true;
            var notifications = response.notifications || [];
            var count = response.count || 0;

            if (isSuccess) {
                // Update the unread count badge
                if (count > 0) {
                    $badge.text(count).show();
                } else {
                    $badge.hide();
                }

                // Render notifications
                if (notifications && notifications.length > 0) {
                    notifications.forEach(function (notif) {
                        // Create a notification item with better structure and action buttons
                        var notificationItem = $('<div class="notification-item unread p-3 border-bottom"></div>');

                        // Content container
                        var contentContainer = $('<div class="notification-content"></div>');
                        contentContainer.append('<h6 class="mb-1 text-primary">' + escapeHtml(notif.title) + '</h6>');
                        contentContainer.append('<p class="mb-1 text-dark">' + escapeHtml(notif.message) + '</p>');
                        contentContainer.append('<small class="text-muted">' + escapeHtml(notif.timeAgo) + '</small>');

                        // Action buttons container
                        var actionsContainer = $('<div class="notification-actions mt-2"></div>');

                        // View button (if action URL exists)
                        if (notif.actionUrl) {
                            var viewButton = $('<a href="' + escapeHtml(notif.actionUrl) + '" class="btn btn-sm btn-primary me-2" title="عرض الطلبات"><i class="fas fa-eye"></i> عرض الطلبات</a>');
                            actionsContainer.append(viewButton);
                        }

                        // Mark as read button (hidden for now since we don't store notifications in DB)
                        // var markAsReadButton = $('<button type="button" class="btn btn-sm btn-outline-secondary" title="تحديد كمقروء"><i class="fas fa-check"></i></button>');
                        // actionsContainer.append(markAsReadButton);

                        // Add content and actions to the notification item
                        notificationItem.append(contentContainer);
                        notificationItem.append(actionsContainer);

                        // Add the notification item to the dropdown
                        $dropdown.append(notificationItem);
                    });
                } else {
                    $dropdown.append('<div class="p-3 text-center text-muted"><i class="fas fa-bell-slash fa-2x mb-2 d-block"></i>لا توجد إشعارات جديدة</div>');
                }
            } else {
                $dropdown.append('<div class="p-3 text-center text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2 d-block"></i>خطأ: ' + escapeHtml(response.error || 'تعذر جلب الإشعارات') + '</div>');
                $badge.hide();
            }
        },
        error: function (xhr, status, error) {
            console.error('Notification fetch error:', error);
            $('#notification-list').html('<div class="p-3 text-center text-danger"><i class="fas fa-exclamation-triangle fa-2x mb-2 d-block"></i>حدث خطأ أثناء جلب الإشعارات.</div>');
            $('#notification-badge').hide();
        }
    });
}

// Helper function to escape HTML to prevent XSS
function escapeHtml(text) {
    if (!text) return '';
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Function to show a SweetAlert notification
function showNotificationAlert(title, message, type = 'info') {
    Swal.fire({
        title: title,
        html: message,
        icon: type,
        confirmButtonText: 'موافق',
        confirmButtonColor: '#3085d6',
        customClass: {
            confirmButton: 'btn btn-primary'
        }
    });
}

// Initialize notifications when document is ready
$(document).ready(function() {
    // Fetch notifications on page load
    fetchUnreadNotifications();

    // Refresh notifications every 30 seconds
    setInterval(fetchUnreadNotifications, 30000);

    // Refresh notifications when dropdown is opened
    $('#notificationIcon').on('click', function() {
        fetchUnreadNotifications();
    });

    // Handle mark all as read form submission
    $('form[action*="MarkAllAsRead"]').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);

        $.ajax({
            url: form.attr('action'),
            type: 'POST',
            data: form.serialize(),
            success: function(response) {
                // Refresh notifications after marking all as read
                fetchUnreadNotifications();
                showNotificationAlert('نجح', 'تم تحديد جميع الإشعارات كمقروءة', 'success');
            },
            error: function() {
                showNotificationAlert('خطأ', 'حدث خطأ أثناء تحديث الإشعارات', 'error');
            }
        });
    });
});


