using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class PathManagementViewModel
    {
        // Manual Routing Properties
        public Dictionary<string, List<string>> PathsConfiguration { get; set; } = new Dictionary<string, List<string>>();
        public List<string> AvailableSupervisors { get; set; } = new List<string>();

        // Auto Routing Properties
        public List<AutoRouteDto> AutoRoutes { get; set; } = new List<AutoRouteDto>();
        public AutoRouteFormViewModel AutoRouteForm { get; set; } = new AutoRouteFormViewModel();
        public int ActiveAutoRoutesCount { get; set; }

        // Direct Routing Properties
        public List<DirectRouteDto> DirectRoutes { get; set; } = new List<DirectRouteDto>();
        public DirectRouteFormViewModel DirectRouteForm { get; set; } = new DirectRouteFormViewModel();
        public int ActiveDirectRoutesCount { get; set; }

        // Dropdown Data
        public List<SelectListItem> OrderTypes { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> JobTypes { get; set; } = new List<SelectListItem>();
        public List<SelectListItem> Nationalities { get; set; } = new List<SelectListItem>();

        // Active Tab
        public string ActiveTab { get; set; } = "manual";
    }

    public class AutoRouteFormViewModel
    {
        public int? Id { get; set; }

        [Required(ErrorMessage = "يرجى اختيار نوع الطلب")]
        [Display(Name = "نوع الطلب")]
        public string OrderType { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الجنسية")]
        [Display(Name = "الجنسية")]
        public string Nationality { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الوظيفة")]
        [Display(Name = "الوظيفة")]
        public string Job { get; set; }

        [Required(ErrorMessage = "يرجى تحديد مشرف واحد على الأقل")]
        [Display(Name = "المشرفين")]
        public List<string> SelectedSupervisors { get; set; } = new List<string>();

        [Display(Name = "حالة المسار")]
        public bool Status { get; set; } = true;

        public bool IsEditing => Id.HasValue && Id != 0;
    }

    public class DirectRouteFormViewModel
    {
        public int? Id { get; set; }

        [Required(ErrorMessage = "يرجى اختيار نوع الطلب")]
        [Display(Name = "نوع الطلب")]
        public string OrderType { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الجنسية")]
        [Display(Name = "الجنسية")]
        public string Nationality { get; set; }

        [Required(ErrorMessage = "يرجى اختيار الوظيفة")]
        [Display(Name = "الوظيفة")]
        public string Job { get; set; }

        [Required(ErrorMessage = "يرجى تحديد مشرف واحد على الأقل")]
        [Display(Name = "المشرفين")]
        public List<string> SelectedSupervisors { get; set; } = new List<string>();

        [Display(Name = "حالة المسار")]
        public bool Status { get; set; } = true;

        public bool IsEditing => Id.HasValue && Id != 0;
    }

    public class UpdatePathSupervisorViewModel
    {
        [Required]
        public string PathColumn { get; set; }

        [Required]
        public string SupervisorText { get; set; }

        [Required]
        public bool IsChecked { get; set; }
    }

    public class PathManagementManualViewModel
    {
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> OrderTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> JobTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> Nationalities { get; set; } = new();
        public List<string> AvailableSupervisors { get; set; } = new();
        public Dictionary<string, List<string>> PathsConfiguration { get; set; } = new();
    }

    public class PathManagementAutoViewModel
    {
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> OrderTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> JobTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> Nationalities { get; set; } = new();
        public List<string> AvailableSupervisors { get; set; } = new();
        public List<AutoRouteDto> AutoRoutes { get; set; } = new();
        public int ActiveAutoRoutesCount { get; set; }
        public AutoRouteFormViewModel AutoRouteForm { get; set; } = new();
    }

    public class PathManagementDirectViewModel
    {
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> OrderTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> JobTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> Nationalities { get; set; } = new();
        public List<string> AvailableSupervisors { get; set; } = new();
        public List<DirectRouteDto> DirectRoutes { get; set; } = new();
        public int ActiveDirectRoutesCount { get; set; }
        public DirectRouteFormViewModel DirectRouteForm { get; set; } = new();
    }

    public class PathManagementDashboardViewModel
    {
        // Manual Routing
        public List<string> AvailableSupervisors { get; set; } = new();
        public Dictionary<string, List<string>> PathsConfiguration { get; set; } = new();
        public int ActiveManualPathsCount { get; set; }

        // Auto Routing
        public List<AutoRouteDto> AutoRoutes { get; set; } = new();
        public int ActiveAutoRoutesCount { get; set; }
        public List<string> AutoSupervisors { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> AutoOrderTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> AutoJobTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> AutoNationalities { get; set; } = new();

        // Direct Routing
        public List<DirectRouteDto> DirectRoutes { get; set; } = new();
        public int ActiveDirectRoutesCount { get; set; }
        public List<string> DirectSupervisors { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> DirectOrderTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> DirectJobTypes { get; set; } = new();
        public List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem> DirectNationalities { get; set; } = new();

        // General Stats
        public int TotalActiveRoutes => ActiveManualPathsCount + ActiveAutoRoutesCount + ActiveDirectRoutesCount;
        public int TotalAvailableSupervisors { get; set; }
    }
}
