using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IDirectRoutingRepository
    {
        Task<List<DirectRouting>> GetAllAsync();
        Task<DirectRouting?> GetByIdAsync(int id);
        Task<DirectRouting> AddAsync(DirectRouting entity);
        Task UpdateAsync(DirectRouting entity);
        Task DeleteAsync(int id);
        Task<bool> ExistsAsync(string orderType, string nationality, string job, int? excludeId = null);
        Task<int> GetActiveCountAsync();
        Task<List<DirectRouting>> GetActiveRoutesAsync();
        Task<DirectRouting?> GetMatchingRouteAsync(string orderType, string nationality, string job);
    }
}
