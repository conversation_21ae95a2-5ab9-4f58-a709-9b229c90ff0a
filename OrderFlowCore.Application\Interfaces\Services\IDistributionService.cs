using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IDistributionService
{
    /// <summary>
    /// Loads all distribution data including current assignments and statistics
    /// </summary>
    Task<ServiceResult<DistributionDto>> GetDistributionDataAsync();

    /// <summary>
    /// Updates a single department's assistant manager assignment
    /// </summary>
    Task<ServiceResult> UpdateDepartmentDistributionAsync(string departmentName, OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId);

    /// <summary>
    /// Updates multiple departments' assistant manager assignments in bulk
    /// </summary>
    Task<ServiceResult<BulkDistributionResult>> BulkUpdateDistributionAsync(List<string> departmentNames, OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId);

    /// <summary>
    /// Clears the assistant manager assignment for a department
    /// </summary>
    Task<ServiceResult> ClearDepartmentDistributionAsync(string departmentName);

    /// <summary>
    /// Generates a comprehensive distribution report
    /// </summary>
    Task<ServiceResult<DistributionReportDto>> GetDistributionReportAsync();

    /// <summary>
    /// Gets the display name for an assistant manager ID
    /// </summary>
    string GetAssistantManagerName(OrderFlowCore.Core.Entities.AssistantManagerType assistantManagerId);
}

public class BulkDistributionResult
{
    public int UpdatedCount { get; set; }
    public List<string> Errors { get; set; } = new();
    public string AssistantManagerName { get; set; } = "";
}

public class DistributionReportDto
{
    public int TotalDepartments { get; set; }
    public int AssignedDepartments { get; set; }
    public int UnassignedDepartments { get; set; }
    public List<ManagerDistributionDto> DistributionByManager { get; set; } = new();
}

public class ManagerDistributionDto
{
    public OrderFlowCore.Core.Entities.AssistantManagerType ManagerId { get; set; } = OrderFlowCore.Core.Entities.AssistantManagerType.Unknown;
    public string ManagerName { get; set; } = "";
    public int DepartmentCount { get; set; }
    public List<string> Departments { get; set; } = new();
}
public class DepartmentDistributionDto
{
    public string DepartmentName { get; set; }
    public OrderFlowCore.Core.Entities.AssistantManagerType AssistantManagerId { get; set; } = OrderFlowCore.Core.Entities.AssistantManagerType.Unknown;
}
public class DistributionStatsDto
{
    public OrderFlowCore.Core.Entities.AssistantManagerType AssistantManager { get; set; } = OrderFlowCore.Core.Entities.AssistantManagerType.Unknown;
    public int TotalDepartments { get; set; }
}
