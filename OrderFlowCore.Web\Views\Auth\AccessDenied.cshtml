@{
    ViewData["Title"] = "Access Denied";
    Layout = "_Layout";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 text-center">
            <div class="mt-5">
                <h1 class="text-danger">403</h1>
                <h2>Access Denied</h2>
                <p class="lead">You don't have permission to access this resource.</p>
                <a asp-controller="Home" asp-action="Index" class="btn btn-primary">Go to Home</a>
            </div>
        </div>
    </div>
</div> 