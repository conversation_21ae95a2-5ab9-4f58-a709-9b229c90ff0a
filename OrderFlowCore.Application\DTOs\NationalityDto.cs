using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs;

public class NationalityDto
{
    public int Id { get; set; }

    [Required(ErrorMessage = "اسم الجنسية مطلوب")]
    [StringLength(100, ErrorMessage = "اسم الجنسية يجب أن لا يتجاوز 100 حرف")]
    [Display(Name = "الجنسية")]
    public string Name { get; set; }

    [Display(Name = "الوصف")]
    public string? Description { get; set; }

    [Display(Name = "نشط")]
    public bool IsActive { get; set; } = true;

    [Display(Name = "تاريخ الإنشاء")]
    public DateTime CreatedAt { get; set; } = DateTime.Now;

    [Required(ErrorMessage = "كود الجنسية مطلوب")]
    [StringLength(10, ErrorMessage = "كود الجنسية يجب أن لا يتجاوز 10 أحرف")]
    [Display(Name = "كود الجنسية")]
    public string Code { get; set; }
} 