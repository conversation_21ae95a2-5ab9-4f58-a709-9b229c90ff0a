using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Services
{
    public class UserService : IUserService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAuthService _authService;
        private readonly IValidationService _validationService;

        public UserService(IUnitOfWork unitOfWork, IAuthService authService, IValidationService validationService)
        {
            _unitOfWork = unitOfWork;
            _authService = authService;
            _validationService = validationService;
        }

        public async Task<ServiceResult<UserProfileDto>> GetProfileAsync(int userId)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(userId);
            if (user == null)
                return ServiceResult<UserProfileDto>.Failure("المستخدم غير موجود");

            var profile = new UserProfileDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                Phone = user.Phone,
                UserRole = user.UserRole,
                RoleType = user.RoleType,
            };

            return ServiceResult<UserProfileDto>.Success(profile);
        }

        public async Task<ServiceResult> UpdateProfileAsync(UserProfileDto dto)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(dto.Id);
            if (user == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            // Validate email using validation service
            var emailValidation = _validationService.ValidateEmail(dto.Email);
            if (!emailValidation.IsSuccess)
                return emailValidation;

            // Check if email already exists (if provided and changed)
            if (!string.IsNullOrEmpty(dto.Email) && dto.Email != user.Email)
            {
                var existingUser = await _unitOfWork.Users.GetByEmailAsync(dto.Email);
                if (existingUser != null && existingUser.Id != dto.Id)
                    return ServiceResult.Failure("البريد الإلكتروني موجود مسبقاً");
            }

            // Validate phone using validation service
            var phoneValidation = _validationService.ValidatePhone(dto.Phone);
            if (!phoneValidation.IsSuccess)
                return phoneValidation;

            // Clean phone number if provided and check uniqueness
            var cleanPhone = dto.Phone;
            if (!string.IsNullOrEmpty(dto.Phone))
            {
                cleanPhone = new string(dto.Phone.Where(char.IsDigit).ToArray());

                // Check if phone already exists (if changed)
                if (cleanPhone != user.Phone)
                {
                    var existingPhone = await _unitOfWork.Users.GetByPhoneAsync(cleanPhone);
                    if (existingPhone != null && existingPhone.Id != dto.Id)
                        return ServiceResult.Failure("رقم الهاتف موجود مسبقاً");
                }
            }

            user.Email = dto.Email?.Trim();
            user.Phone = cleanPhone?.Trim();
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم تحديث الملف الشخصي بنجاح");
        }

        public async Task<ServiceResult> ChangePasswordAsync(int userId, ChangePasswordDto dto)
        {
            var user = await _unitOfWork.Users.GetUserByIdAsync(userId);
            if (user == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            if (!_authService.VerifyPassword(dto.CurrentPassword, user.Password))
                return ServiceResult.Failure("كلمة المرور الحالية غير صحيحة");

            if (dto.CurrentPassword == dto.NewPassword)
                return ServiceResult.Failure("كلمة المرور الجديدة يجب أن تكون مختلفة عن كلمة المرور الحالية");

            // Use validation service for password validation
            var passwordValidation = _validationService.ValidatePassword(dto.NewPassword, 8);
            if (!passwordValidation.IsSuccess)
                return passwordValidation;

            var confirmationValidation = _validationService.ValidatePasswordConfirmation(dto.NewPassword, dto.ConfirmPassword);
            if (!confirmationValidation.IsSuccess)
                return confirmationValidation;

            user.Password = _authService.HashPassword(dto.NewPassword);
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم تغيير كلمة المرور بنجاح");
        }

        // Account Management Methods
        public async Task<ServiceResult<List<UserDto>>> GetAllUsersAsync()
        {
            try
            {
                var users = await _unitOfWork.Users.GetAllAsync();
                var userDtos = users.Select(u => new UserDto
                {
                    Id = u.Id,
                    Username = u.Username,
                    Email = u.Email,
                    Phone = u.Phone,
                    UserRole = u.UserRole,
                    RoleType = u.RoleType,
                    IsActive = u.IsActive
                }).ToList();

                return ServiceResult<List<UserDto>>.Success(userDtos);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<UserDto>>.Failure($"خطأ في استرجاع المستخدمين: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> GetUserByUsernameAsync(string username)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByUsernameAsync(username);
                if (user == null)
                    return ServiceResult<UserDto>.Failure("المستخدم غير موجود");

                var userDto = new UserDto
                {
                    Id = user.Id,
                    Username = user.Username,
                    Email = user.Email,
                    Phone = user.Phone,
                    UserRole = user.UserRole,
                    RoleType = user.RoleType,
                    IsActive = user.IsActive
                };

                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"خطأ في استرجاع المستخدم: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> CreateUserAsync(UserDto userDto)
        {
            try
            {
                // Validate username
                var usernameValidation = _validationService.ValidateUsername(userDto.Username);
                if (!usernameValidation.IsSuccess)
                    return ServiceResult<UserDto>.Failure(usernameValidation.Message);

                // Validate email
                var emailValidation = _validationService.ValidateEmail(userDto.Email);
                if (!emailValidation.IsSuccess)
                    return ServiceResult<UserDto>.Failure(emailValidation.Message);

                // Validate phone
                var phoneValidation = _validationService.ValidatePhone(userDto.Phone);
                if (!phoneValidation.IsSuccess)
                    return ServiceResult<UserDto>.Failure(phoneValidation.Message);


                var passwordValidation = _validationService.ValidatePassword(userDto.Password);
                if (!passwordValidation.IsSuccess)
                    return ServiceResult<UserDto>.Failure(passwordValidation.Message);


                // Check if username already exists
                var existingUser = await _unitOfWork.Users.GetByUsernameAsync(userDto.Username);
                if (existingUser != null)
                    return ServiceResult<UserDto>.Failure("اسم المستخدم موجود مسبقاً");

                // Check if email already exists (if provided)
                if (!string.IsNullOrEmpty(userDto.Email))
                {
                    var existingEmail = await _unitOfWork.Users.GetByEmailAsync(userDto.Email);
                    if (existingEmail != null)
                        return ServiceResult<UserDto>.Failure("البريد الإلكتروني موجود مسبقاً");
                }

                // Clean phone number if provided
                var cleanPhone = userDto.Phone;
                if (!string.IsNullOrEmpty(userDto.Phone))
                {
                    cleanPhone = new string(userDto.Phone.Where(char.IsDigit).ToArray());

                    // Check if phone already exists
                    var existingPhone = await _unitOfWork.Users.GetByPhoneAsync(cleanPhone);
                    if (existingPhone != null)
                        return ServiceResult<UserDto>.Failure("رقم الهاتف موجود مسبقاً");
                }

                var user = new User
                {
                    Username = userDto.Username.Trim(),
                    Email = userDto.Email?.Trim(),
                    Phone = cleanPhone?.Trim(),
                    Password = userDto.Password.StartsWith("$") ? userDto.Password : _authService.HashPassword(userDto.Password),
                    UserRole = userDto.UserRole,
                    RoleType = userDto.RoleType
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                userDto.Id = user.Id;
                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"خطأ في إنشاء المستخدم: {ex.Message}");
            }
        }

        public async Task<ServiceResult<UserDto>> UpdateUserAsync(UserDto userDto)
        {
            try
            {
                var user = await _unitOfWork.Users.GetUserByIdAsync(userDto.Id);
                if (user == null)
                    return ServiceResult<UserDto>.Failure("المستخدم غير موجود");

                // Check if email already exists (if changed)
                if (!string.IsNullOrEmpty(userDto.Email) && userDto.Email != user.Email)
                {
                    var existingEmail = await _unitOfWork.Users.GetByEmailAsync(userDto.Email);
                    if (existingEmail != null)
                        return ServiceResult<UserDto>.Failure("البريد الإلكتروني موجود مسبقاً");
                }

                // Clean and check phone uniqueness (if changed)
                var cleanPhone = userDto.Phone;
                if (!string.IsNullOrEmpty(userDto.Phone))
                {
                    cleanPhone = new string(userDto.Phone.Where(char.IsDigit).ToArray());

                    if (cleanPhone != user.Phone)
                    {
                        var existingPhone = await _unitOfWork.Users.GetByPhoneAsync(cleanPhone);
                        if (existingPhone != null && existingPhone.Id != user.Id)
                            return ServiceResult<UserDto>.Failure("رقم الهاتف موجود مسبقاً");
                    }
                }

                user.Email = userDto.Email?.Trim();
                user.Phone = cleanPhone?.Trim();

                await _unitOfWork.Users.UpdateAsync(user);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult<UserDto>.Success(userDto);
            }
            catch (Exception ex)
            {
                return ServiceResult<UserDto>.Failure($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteUserAsync(string username)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByUsernameAsync(username);
                if (user == null)
                    return ServiceResult.Failure("المستخدم غير موجود");

                // Use validation service to check if user is protected
                if (_validationService.IsProtectedUser(username))
                    return ServiceResult.Failure("لا يمكن حذف المستخدم المحمي");

                await _unitOfWork.Users.DeleteAsync(user.Id);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم حذف المستخدم بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في حذف المستخدم: {ex.Message}");
            }
        }

        public async Task<ServiceResult> SetUserPasswordAsync(string username, string newPassword)
        {
            var user = await _unitOfWork.Users.GetByUsernameAsync(username);
            if (user == null)
                return ServiceResult.Failure("المستخدم غير موجود");

            // Use validation service for password validation
            var passwordValidation = _validationService.ValidatePassword(newPassword);
            if (!passwordValidation.IsSuccess)
                return passwordValidation;

            user.Password = _authService.HashPassword(newPassword);
            await _unitOfWork.Users.UpdateAsync(user);
            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم تغيير كلمة المرور بنجاح");
        }

        public async Task<ServiceResult> SetUserActiveAsync(string username, bool isActive)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByUsernameAsync(username);
                if (user == null)
                    return ServiceResult.Failure("المستخدم غير موجود");

                var entity = await _unitOfWork.Users.GetUserByIdAsync(user.Id);
                if (entity == null)
                    return ServiceResult.Failure("المستخدم غير موجود");

                entity.IsActive = isActive;
                await _unitOfWork.Users.UpdateAsync(entity);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success(isActive ? "تم تفعيل المستخدم" : "تم تعطيل المستخدم");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تحديث حالة المستخدم: {ex.Message}");
            }
        }
    }
}