using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IEmployeeRepository
    {
        Task<EmployeeDto?> GetByCivilNumberAsync(string civilNumber);
        Task<IEnumerable<EmployeeDto>> GetAllAsync();
        Task<EmployeeDto?> GetByIdAsync(int id);
        Task<bool> CreateAsync(EmployeeDto dto);
        Task<bool> UpdateAsync(EmployeeDto dto);
        Task<bool> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
    }
} 