using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class GeneralStatisticsDto
    {
        public int TotalRequests { get; set; }
        public int CompletedRequests { get; set; }
        public int PendingRequests { get; set; }
        public double AverageTimeToComplete { get; set; }
    }

    public class StageStatisticsDto
    {
        public int DepartmentManager { get; set; }
        public int AssistantManagerA1 { get; set; }
        public int AssistantManagerA2 { get; set; }
        public int AssistantManagerA3 { get; set; }
        public int AssistantManagerA4 { get; set; }
        public int HRCoordinator { get; set; }
        public int Supervisors { get; set; }
        public int HRManager { get; set; }
    }

    public class OrderDepartmentStatisticsDto
    {
        public string DepartmentName { get; set; } = string.Empty;
        public int TotalOrders { get; set; }
        public int CompletedOrders { get; set; }
        public int PendingOrders { get; set; }
        public int CancelledOrders { get; set; }
    }

    public class SupervisorStatisticsDto
    {
        public string SupervisorName { get; set; } = string.Empty;
        public int UnderExecution { get; set; }
        public int Completed { get; set; }
        public int NeedsAction { get; set; }
        public int Returned { get; set; }
        public double AverageCompletionTime { get; set; }
    }

    public class AssistantManagerStatisticsDto
    {
        public string AssistantManagerName { get; set; } = string.Empty;
        public int UnderExecution { get; set; }
        public int Completed { get; set; }
        public int Returned { get; set; }
        public int Cancelled { get; set; }
        public double AverageCompletionTime { get; set; }
    }


    public class TransferTypeStatisticsDto
    {
        public string TransferType { get; set; } = string.Empty;
        public int Count { get; set; }
    }

    public class StatisticsViewModel
    {
        public GeneralStatisticsDto GeneralStatistics { get; set; } = new();
        public StageStatisticsDto StageStatistics { get; set; } = new();
        public List<OrderDepartmentStatisticsDto> DepartmentStatistics { get; set; } = new();
        public List<SupervisorStatisticsDto> SupervisorStatistics { get; set; } = new();
        public List<AssistantManagerStatisticsDto> AssistantManagerStatistics { get; set; } = new();
        public List<TransferTypeStatisticsDto> TransferTypeStatistics { get; set; } = new();
    }
}