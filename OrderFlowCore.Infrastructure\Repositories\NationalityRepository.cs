using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Infrastructure.Data;

public class NationalityRepository : INationalityRepository
{
    private readonly ApplicationDbContext _context;

    public NationalityRepository(ApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<IEnumerable<NationalityDto>> GetAllAsync()
    {
        var nationalities = await _context.Nationalities
            .AsNoTracking()
            .OrderBy(n => n.Name)
            .ToListAsync();

        return nationalities.Select(MapToDto);
    }

    public async Task<NationalityDto?> GetByIdAsync(int id)
    {
        var nationality = await _context.Nationalities
            .FirstOrDefaultAsync(n => n.Id == id);

        return nationality != null ? MapToDto(nationality) : null;
    }

    public async Task<bool> CreateAsync(NationalityDto nationalityDto)
    {
        var nationality = MapToEntity(nationalityDto);
        await _context.Nationalities.AddAsync(nationality);
        
        return true;
    }

    public async Task<bool> UpdateAsync(NationalityDto nationalityDto)
    {
        var nationality = await _context.Nationalities.FindAsync(nationalityDto.Id);
        if (nationality == null)
            return false;

        nationality.Name = nationalityDto.Name;
        nationality.Description = nationalityDto.Description;
        nationality.IsActive = nationalityDto.IsActive;
        
        return true;
    }

    public async Task<bool> DeleteAsync(int id)
    {
        var nationality = await _context.Nationalities.FindAsync(id);
        if (nationality == null)
            return false;

        _context.Nationalities.Remove(nationality);
        
        return true;
    }

    public async Task<bool> ExistsAsync(string name, int? excludeId = null)
    {
        var query = _context.Nationalities.Where(n => n.Name == name);
        
        if (excludeId.HasValue)
        {
            query = query.Where(n => n.Id != excludeId.Value);
        }

        return await query.AnyAsync();
    }

    private static NationalityDto MapToDto(Nationality nationality)
    {
        return new NationalityDto
        {
            Id = nationality.Id,
            Name = nationality.Name,
            Description = nationality.Description,
            IsActive = nationality.IsActive,
            Code = nationality.Code,
            CreatedAt = nationality.CreatedAt
        };
    }

    private static Nationality MapToEntity(NationalityDto nationalityDto)
    {
        return new Nationality
        {
            Id = nationalityDto.Id,
            Name = nationalityDto.Name,
            Description = nationalityDto.Description,
            IsActive = nationalityDto.IsActive,
            CreatedAt = nationalityDto.CreatedAt
        };
    }
} 