﻿@model OrderFlowCore.Web.ViewModels.HRCoordinatorViewModel
@{
    ViewData["Title"] = "معالجة الطلب";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>معالجة الطلب #@Model.SelectedOrderId</h2>
                <a href="@Url.Action("Index", "HRCoordinator")" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للقائمة الرئيسية
                </a>
            </div>

            <!-- Alert Panels -->
            <div id="AutoPathInfoPanel" style="display: none;" class="alert alert-info mb-4"></div>
            <div id="RejectionAlertPanel" style="display: none;" class="mb-3"></div>

            @using (Html.BeginForm())
            {
                @Html.AntiForgeryToken()
                @Html.HiddenFor(m => m.SelectedOrderId)

                <!-- Main Processing Section -->
                <div class="row">
                    <div class="col-12">
                        @Html.Raw(Model.AutoRoutingInfo.Message)
                    </div>

                    <!-- Right Column: Actions -->
                    <div class="col-lg-4">
                        <!-- Primary Actions -->
                        <div class="card mb-3 border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0"><i class="fas fa-play-circle"></i> الإجراءات الرئيسية</h6>
                            </div>
                            <div class="card-body">
                                <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="HRCoordinator.submitOrder()">
                                    <i class="fas fa-check-circle"></i> تحويل الطلب
                                </button>
                                <button type="button" class="btn btn-info btn-lg w-100 mb-3" onclick="HRCoordinator.markNeedsAction()">
                                    <i class="fas fa-exclamation-triangle"></i> يتطلب إجراءات
                                </button>
                                <button type="button" class="btn btn-warning btn-lg w-100 mb-3" onclick="HRCoordinator.returnOrder()">
                                    <i class="fas fa-undo"></i> إعادة الطلب
                                </button>
                                <button type="button" class="btn btn-danger btn-lg w-100" onclick="HRCoordinator.rejectOrder()">
                                    <i class="fas fa-times-circle"></i> إلغاء الطلب
                                </button>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="card mb-3">
                            <div class="card-header bg-info text-white">
                                <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                            </div>
                            <div class="card-body">
                                @if (Model.AutoRoutingInfo.IsAvailable)
                                {
                                    <button type="button" class="btn btn-outline-info w-100 mb-2" onclick="HRCoordinator.autoRouteOrder()">
                                        <i class="fas fa-magic"></i> التوجيه التلقائي
                                    </button>
                                }
                                else
                                {
                                    <button type="button" class="btn btn-outline-info w-100 mb-2" disabled>
                                        <i class="fas fa-magic"></i> التوجيه التلقائي (غير مفعل)
                                    </button>
                                }
                                
                                <button type="button" class="btn btn-outline-danger w-100 mb-2" onclick="HRCoordinator.directToManager()">
                                    <i class="fas fa-user-tie"></i> تحويل مباشر للمدير
                                </button>
                                <button id="downloadAttachmentsBtn" class="btn btn-outline-secondary w-100" onclick="HRCoordinator.downloadAttachments()">
                                    <i class="fas fa-download"></i> تحميل المرفقات
                                </button>
                            </div>
                        </div>

                        <!-- Predefined Paths -->
                        <div class="card">
                            <div class="card-header bg-secondary text-white">
                                <h6 class="mb-0"><i class="fas fa-route"></i> المسارات المحددة</h6>
                            </div>
                            <div class="card-body">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(1)">مسار 1</button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(2)">مسار 2</button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(3)">مسار 3</button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(4)">مسار 4</button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(5)">مسار 5</button>
                                    </div>
                                    <div class="col-6">
                                        <button type="button" class="btn btn-outline-secondary btn-sm w-100" onclick="HRCoordinator.applyPath(6)">مسار 6</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Left Column: Form Fields -->
                    <div class="col-lg-8">
                        <!-- Form Details Card -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0"><i class="fas fa-edit"></i> تفاصيل المعالجة</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="details" class="form-label fw-bold">التفاصيل والرقم</label>
                                    <textarea type="text" class="form-control" id="details" name="details" placeholder="أدخل التفاصيل والرقم هنا..." rows="3"></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="actionRequired" class="form-label fw-bold">الإجراءات المطلوبة</label>
                                    <textarea type="text" class="form-control" id="actionRequired" name="actionRequired" placeholder="حدد الإجراءات المطلوبة..."></textarea>
                                </div>

                                <div class="mb-3">
                                    <label for="returnReason" class="form-label fw-bold">سبب الإلغاء/الإعادة</label>
                                    <textarea type="text" class="form-control" id="returnReason" name="returnReason" placeholder="اذكر سبب الإلغاء أو الإعادة..." rows="2"></textarea>
                                </div>
                            </div>
                        </div>
                        <!-- Message Container -->
                        <div id="messageContainer" class="mt-3"></div>

                        <!-- Departments Selection Card -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0"><i class="fas fa-building"></i> اختيار الأقسام للاعتماد</h5>
                                    <div>
                                        <button type="button" class="btn btn-sm btn-light me-2" onclick="HRCoordinator.selectAllSupervisors()">
                                            <i class="fas fa-check-square"></i> تحديد الكل
                                        </button>
                                        <button type="button" class="btn btn-sm btn-outline-light" onclick="HRCoordinator.unselectAllSupervisors()">
                                            <i class="fas fa-square"></i> إلغاء الكل
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-3">الأقسام الإدارية</h6>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk1" name="SelectedSupervisors" value="خدمات الموظفين" />
                                            <label class="form-check-label" for="chk1">خدمات الموظفين</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk2" name="SelectedSupervisors" value="إدارة تخطيط الموارد البشرية" />
                                            <label class="form-check-label" for="chk2">إدارة تخطيط الموارد البشرية</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk3" name="SelectedSupervisors" value="إدارة تقنية المعلومات" />
                                            <label class="form-check-label" for="chk3">إدارة تقنية المعلومات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk4" name="SelectedSupervisors" value="مراقبة الدوام" />
                                            <label class="form-check-label" for="chk4">مراقبة الدوام</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk6" name="SelectedSupervisors" value="إدارة الرواتب والاستحقاقات" />
                                            <label class="form-check-label" for="chk6">إدارة الرواتب والاستحقاقات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk7" name="SelectedSupervisors" value="إدارة القانونية والالتزام" />
                                            <label class="form-check-label" for="chk7">إدارة القانونية والالتزام</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk8" name="SelectedSupervisors" value="خدمات الموارد البشرية" />
                                            <label class="form-check-label" for="chk8">خدمات الموارد البشرية</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk12" name="SelectedSupervisors" value="التأمينات الاجتماعية" />
                                            <label class="form-check-label" for="chk12">التأمينات الاجتماعية</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6 class="text-muted mb-3">الأقسام الأخرى</h6>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk5" name="SelectedSupervisors" value="السجلات الطبية" />
                                            <label class="form-check-label" for="chk5">السجلات الطبية</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk11" name="SelectedSupervisors" value="العيادات الخارجية" />
                                            <label class="form-check-label" for="chk11">العيادات الخارجية</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk9" name="SelectedSupervisors" value="إدارة الإسكان" />
                                            <label class="form-check-label" for="chk9">إدارة الإسكان</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk10" name="SelectedSupervisors" value="قسم الملفات" />
                                            <label class="form-check-label" for="chk10">قسم الملفات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk13" name="SelectedSupervisors" value="وحدة مراقبة المخزون" />
                                            <label class="form-check-label" for="chk13">وحدة مراقبة المخزون</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk14" name="SelectedSupervisors" value="إدارة تنمية الإيرادات" />
                                            <label class="form-check-label" for="chk14">إدارة تنمية الإيرادات</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk15" name="SelectedSupervisors" value="إدارة الأمن و السلامة" />
                                            <label class="form-check-label" for="chk15">إدارة الأمن و السلامة</label>
                                        </div>
                                        <div class="form-check mb-2">
                                            <input type="checkbox" class="form-check-input" id="chk16" name="SelectedSupervisors" value="الطب الاتصالي" />
                                            <label class="form-check-label" for="chk16">الطب الاتصالي</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }



            <!-- Order Details Summary -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-file-alt"></i> ملخص الطلب</h5>
                </div>
                <div class="card-body">
                    @await Html.PartialAsync("_OrderDetailsPartial")
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Submit Modal -->
<div class="modal fade" id="confirmSubmitModal" tabindex="-1" aria-labelledby="confirmSubmitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmSubmitModalLabel">تأكيد التحويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من تحويل الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSubmitModalBtn">نعم، تحويل</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Action Required Modal -->
<div class="modal fade" id="confirmActionRequiredModal" tabindex="-1" aria-labelledby="confirmActionRequiredModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmActionRequiredModalLabel">تأكيد الإجراء المطلوب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من أن هذا الطلب يتطلب إجراءات إضافية؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="confirmActionRequiredModalBtn">نعم، يتطلب إجراءات</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Auto Path Modal -->
<div class="modal fade" id="confirmAutoPathModal" tabindex="-1" aria-labelledby="confirmAutoPathModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmAutoPathModalLabel">تأكيد التوجيه التلقائي</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من تطبيق التوجيه التلقائي؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">لا</button>
                <button type="button" class="btn btn-primary" id="confirmAutoPathModalBtn">نعم</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Direct to Manager Modal -->
<div class="modal fade" id="confirmDirectToManagerModal" tabindex="-1" aria-labelledby="confirmDirectToManagerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmDirectToManagerModalLabel">تأكيد التحويل المباشر</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                <div class="text-danger">
                    <strong>تنبيه هام جداً:</strong><br>
                    هذا الإجراء سيقوم بتحويل الطلب مباشرة إلى مدير الموارد البشرية،<br>
                    متجاوزاً جميع المشرفين.<br><br>
                    هل أنت متأكد من المتابعة؟
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmDirectToManagerModalBtn">نعم، متابعة</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Return Modal -->
<div class="modal fade" id="confirmReturnModal" tabindex="-1" aria-labelledby="confirmReturnModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmReturnModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إعادة الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmReturnModalBtn">نعم، إعادة</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Reject Modal -->
<div class="modal fade" id="confirmRejectModal" tabindex="-1" aria-labelledby="confirmRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmRejectModalLabel">تأكيد إلغاء الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إلغاء الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRejectModalBtn">نعم، إلغاء</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        window.pathsConfiguration = @Html.Raw(System.Text.Json.JsonSerializer.Serialize(Model.PathsConfiguration))
    </script>
    <script src="~/js/shared-utils.js"></script>
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrCoordinator.js"></script>
    <script>
        // Show success/error messages from TempData
        @if (TempData["SuccessMessage"] != null)
        {
                <text>OrderDetailsModule.showMessage('@TempData["SuccessMessage"]', 'success');</text>
        }
        @if (TempData["ErrorMessage"] != null)
        {
                <text>OrderDetailsModule.showMessage('@TempData["ErrorMessage"]', 'error');</text>
        }
    </script>

    <style>
        /* Custom styling for better visual hierarchy */
        .btn-lg {
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
        }

        .card-header h5, .card-header h6 {
            margin-bottom: 0;
        }

        .form-check-label {
            cursor: pointer;
            user-select: none;
        }

        .form-check-input:checked ~ .form-check-label {
            font-weight: 500;
            color: #0d6efd;
        }

        /* Visual separation for different action types */
        .border-success {
            border-width: 2px !important;
        }

        /* Improve button hover states */
        .btn-outline-secondary:hover {
            background-color: #6c757d;
            color: white;
        }

        /* Make form fields more prominent */
        .form-control:focus {
            border-color: #0d6efd;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }

        /* Icon spacing */
        .btn i {
            margin-right: 0.5rem;
        }
    </style>
}