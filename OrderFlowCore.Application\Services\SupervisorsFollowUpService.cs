using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;
using System.IO;
using System.Text;
using OrderFlowCore.Application.Common;
using System;

namespace OrderFlowCore.Application.Services
{
    public class SupervisorsFollowUpService : ISupervisorsFollowUpService
    {
        private readonly IUnitOfWork _unitOfWork;
        public SupervisorsFollowUpService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<List<SupervisorsFollowUpDto>>> GetBySupervisorAsync(string supervisorId)
        {
            try
            {
                var records = await _unitOfWork.SupervisorsFollowUps.GetBySupervisorAsync(supervisorId);
                var result = records.OrderBy(x => x.OwnerName).Select(MapToDto).ToList();
                return ServiceResult<List<SupervisorsFollowUpDto>>.Success(result, "تم جلب السجلات بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult<List<SupervisorsFollowUpDto>>.Failure($"حدث خطأ أثناء جلب السجلات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<SupervisorsFollowUpDto>> GetAsync(string supervisorId, string civilRecord)
        {
            try
            {
                var entity = await _unitOfWork.SupervisorsFollowUps.GetAsync(supervisorId, civilRecord);
                if (entity == null)
                    return ServiceResult<SupervisorsFollowUpDto>.Failure("السجل غير موجود.");
                return ServiceResult<SupervisorsFollowUpDto>.Success(MapToDto(entity), "تم جلب السجل بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult<SupervisorsFollowUpDto>.Failure($"حدث خطأ أثناء جلب السجل: {ex.Message}");
            }
        }

        public async Task<ServiceResult> AddAsync(SupervisorsFollowUpDto record)
        {
            try
            {
                var exists = await _unitOfWork.SupervisorsFollowUps.AnyAsync(record.SupervisorId, record.CivilRecord);
                if (exists)
                {
                    return ServiceResult.Failure("السجل موجود بالفعل لهذا المشرف ورقم السجل المدني.");
                }
                var entity = MapToEntity(record);
                await _unitOfWork.SupervisorsFollowUps.AddAsync(entity);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تمت إضافة السجل بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"حدث خطأ أثناء إضافة السجل: {ex.Message}");
            }
        }

        public async Task<ServiceResult> UpdateAsync(SupervisorsFollowUpDto record)
        {
            try
            {
                var entity = await _unitOfWork.SupervisorsFollowUps.GetAsync(record.SupervisorId, record.CivilRecord);
                if (entity != null)
                {
                    entity.OwnerName = record.OwnerName;
                    entity.SpecialProcedure = record.SpecialProcedure;
                    await _unitOfWork.SupervisorsFollowUps.UpdateAsync(entity);
                    await _unitOfWork.SaveChangesAsync();
                    return ServiceResult.Success("تم تحديث السجل بنجاح.");
                }
                return ServiceResult.Failure("السجل غير موجود.");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"حدث خطأ أثناء تحديث السجل: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteAsync(string supervisorId, string civilRecord)
        {
            try
            {
                await _unitOfWork.SupervisorsFollowUps.DeleteAsync(supervisorId, civilRecord);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم حذف السجل بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"حدث خطأ أثناء حذف السجل: {ex.Message}");
            }
        }

        public async Task<ServiceResult> DeleteAllAsync(string supervisorId)
        {
            try
            {
                await _unitOfWork.SupervisorsFollowUps.DeleteAllAsync(supervisorId);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم حذف جميع السجلات بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"حدث خطأ أثناء حذف جميع السجلات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportAsync(string supervisorId, Stream csvStream)
        {
            try
            {
                int count = 0;
                using (var reader = new StreamReader(csvStream, Encoding.UTF8))
                {
                    bool isFirstLine = true;
                    while (!reader.EndOfStream)
                    {
                        var line = await reader.ReadLineAsync();
                        if (isFirstLine)
                        {
                            isFirstLine = false;
                            continue; // skip header
                        }
                        if (string.IsNullOrWhiteSpace(line)) continue;
                        var fields = line.Split(',');
                        if (fields.Length < 3) continue;
                        var civilRecord = fields[0].Trim();
                        var ownerName = fields[1].Trim();
                        var specialProcedure = fields[2].Trim();
                        // Check for duplicate
                        var exists = await _unitOfWork.SupervisorsFollowUps.AnyAsync(supervisorId, civilRecord);
                        if (exists) continue;
                        var entity = new SupervisorsFollowUp
                        {
                            SupervisorId = supervisorId,
                            CivilRecord = civilRecord,
                            OwnerName = ownerName,
                            SpecialProcedure = specialProcedure
                        };
                        await _unitOfWork.SupervisorsFollowUps.AddAsync(entity);
                        count++;
                    }
                    await _unitOfWork.SaveChangesAsync();
                }
                return ServiceResult<int>.Success(count, $"تم استيراد {count} سجل بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"حدث خطأ أثناء استيراد السجلات: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportAsync(string supervisorId)
        {
            try
            {
                var records = await _unitOfWork.SupervisorsFollowUps.GetBySupervisorAsync(supervisorId);
                var sb = new StringBuilder();
                sb.AppendLine("SupervisorId;CivilRecord;OwnerName;SpecialProcedure");
                foreach (var r in records)
                {
                    sb.AppendLine($"{r.SupervisorId};{r.CivilRecord};{r.OwnerName};{r.SpecialProcedure}");
                }
                return ServiceResult<byte[]>.Success(Encoding.UTF8.GetBytes(sb.ToString()), "تم تصدير السجلات بنجاح.");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"حدث خطأ أثناء تصدير السجلات: {ex.Message}");
            }
        }

        private SupervisorsFollowUpDto MapToDto(SupervisorsFollowUp entity)
        {
            return new SupervisorsFollowUpDto
            {
                SupervisorId = entity.SupervisorId,
                CivilRecord = entity.CivilRecord,
                OwnerName = entity.OwnerName,
                SpecialProcedure = entity.SpecialProcedure
            };
        }

        private SupervisorsFollowUp MapToEntity(SupervisorsFollowUpDto dto)
        {
            return new SupervisorsFollowUp
            {
                SupervisorId = dto.SupervisorId,
                CivilRecord = dto.CivilRecord,
                OwnerName = dto.OwnerName,
                SpecialProcedure = dto.SpecialProcedure
            };
        }
    }
}