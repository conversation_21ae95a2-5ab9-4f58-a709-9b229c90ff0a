using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;
using System.Text;

namespace OrderFlowCore.Application.Services
{
    public class EmployeeService : IEmployeeService
    {
        private readonly IUnitOfWork _unitOfWork;
        
        public EmployeeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<EmployeeDto>> GetByCivilNumberAsync(string civilNumber)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByCivilNumberAsync(civilNumber);
                if (employee == null)
                    return ServiceResult<EmployeeDto>.Failure("الموظف غير موجود");

                return ServiceResult<EmployeeDto>.Success(employee);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmployeeDto>.Failure($"خطأ في استرجاع الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<IEnumerable<EmployeeDto>>> GetAllAsync()
        {
            try
            {
                var employees = await _unitOfWork.Employees.GetAllAsync();
                return ServiceResult<IEnumerable<EmployeeDto>>.Success(employees);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<EmployeeDto>>.Failure($"خطأ في استرجاع الموظفين: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<EmployeeDto>> GetByIdAsync(int id)
        {
            try
            {
                var employee = await _unitOfWork.Employees.GetByIdAsync(id);
                if (employee == null)
                    return ServiceResult<EmployeeDto>.Failure("الموظف غير موجود");

                return ServiceResult<EmployeeDto>.Success(employee);
            }
            catch (Exception ex)
            {
                return ServiceResult<EmployeeDto>.Failure($"خطأ في استرجاع الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(EmployeeDto dto)
        {
            try
            {
                var result = await _unitOfWork.Employees.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم إنشاء الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في إنشاء الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إنشاء الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(EmployeeDto dto)
        {
            try
            {
                var result = await _unitOfWork.Employees.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("تم تحديث الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في تحديث الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في تحديث الموظف: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.Employees.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                if (result)
                    return ServiceResult.Success("تم حذف الموظف بنجاح");
                else
                    return ServiceResult.Failure("فشل في حذف الموظف");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في حذف الموظف: {ex.Message}");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
        {
            try
            {
                var employees = await _unitOfWork.Employees.GetAllAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الموظفين");

                // Headers in Arabic
                worksheet.Cells[1, 1].Value = "الاسم";
                worksheet.Cells[1, 2].Value = "الوظيفة";
                worksheet.Cells[1, 3].Value = "رقم الموظف";
                worksheet.Cells[1, 4].Value = "الرقم المدني";
                worksheet.Cells[1, 5].Value = "الجنسية";
                worksheet.Cells[1, 6].Value = "رقم الهاتف";
                worksheet.Cells[1, 7].Value = "نوع التوظيف";
                worksheet.Cells[1, 8].Value = "المؤهل";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 8])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Data rows
                int row = 2;
                foreach (var employee in employees)
                {
                    worksheet.Cells[row, 1].Value = employee.Name;
                    worksheet.Cells[row, 2].Value = employee.Job;
                    worksheet.Cells[row, 3].Value = employee.EmployeeNumber;
                    worksheet.Cells[row, 4].Value = employee.CivilNumber;
                    worksheet.Cells[row, 5].Value = employee.Nationality;
                    worksheet.Cells[row, 6].Value = employee.Mobile;
                    worksheet.Cells[row, 7].Value = employee.EmploymentType;
                    worksheet.Cells[row, 8].Value = employee.Qualification;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var excelData = package.GetAsByteArray();
                return ServiceResult<byte[]>.Success(excelData, "تم تصدير الموظفين بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تصدير الموظفين: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                        var job = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                        var employeeNumber = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                        var civilNumber = worksheet.Cells[row, 4].Value?.ToString()?.Trim();
                        var nationality = worksheet.Cells[row, 5].Value?.ToString()?.Trim();
                        var mobile = worksheet.Cells[row, 6].Value?.ToString()?.Trim();
                        var employmentType = worksheet.Cells[row, 7].Value?.ToString()?.Trim();
                        var qualification = worksheet.Cells[row, 8].Value?.ToString()?.Trim();

                        // Validate required fields
                        if (string.IsNullOrEmpty(name))
                        {
                            errors.Add($"الصف {row}: اسم الموظف مطلوب");
                            continue;
                        }

                        if (string.IsNullOrEmpty(civilNumber))
                        {
                            errors.Add($"الصف {row}: الرقم المدني مطلوب");
                            continue;
                        }

                        // Check if employee already exists
                        var existingEmployee = await _unitOfWork.Employees.GetByCivilNumberAsync(civilNumber);
                        if (existingEmployee != null)
                        {
                            errors.Add($"الصف {row}: موظف بالرقم المدني {civilNumber} موجود بالفعل");
                            continue;
                        }

                        var employeeDto = new EmployeeDto
                        {
                            Name = name,
                            Job = job,
                            EmployeeNumber = employeeNumber,
                            CivilNumber = civilNumber,
                            Nationality = nationality,
                            Mobile = mobile,
                            EmploymentType = employmentType,
                            Qualification = qualification,
                        };

                        var createResult = await _unitOfWork.Employees.CreateAsync(employeeDto);
                        if (createResult)
                        {
                            importedCount++;
                        }
                        else
                        {
                            errors.Add($"الصف {row}: فشل في إضافة الموظف {name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                var message = $"تم استيراد {importedCount} موظف بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                var finalResult = ServiceResult<int>.Success(importedCount, message);
                finalResult.Errors = errors;
                return finalResult;
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في استيراد الموظفين: {ex.Message}");
            }
        }
    }
}