using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;

namespace OrderFlowCore.Application.Services;

public class NationalityService : INationalityService
{
    private readonly IUnitOfWork _unitOfWork;

    public NationalityService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ServiceResult<IEnumerable<NationalityDto>>> GetAllAsync()
    {
        try
        {
            var nationalities = await _unitOfWork.Nationalities.GetAllAsync();
            return ServiceResult<IEnumerable<NationalityDto>>.Success(nationalities);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<NationalityDto>>.Failure($"خطأ في استرجاع الجنسيات: {ex.Message}");
        }
    }

    public async Task<ServiceResult<NationalityDto>> GetByIdAsync(int id)
    {
        try
        {
            var nationality = await _unitOfWork.Nationalities.GetByIdAsync(id);
            if (nationality == null)
                return ServiceResult<NationalityDto>.Failure("Nationality not found");
                
            return ServiceResult<NationalityDto>.Success(nationality);
        }
        catch (Exception ex)
        {
            return ServiceResult<NationalityDto>.Failure($"Error retrieving nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult> CreateAsync(NationalityDto dto)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.CreateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            
            if (result)
                return ServiceResult.Success("تم إنشاء الجنسية بنجاح");
            else
                return ServiceResult.Failure("فشل في إنشاء الجنسية");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"خطأ في إنشاء الجنسية: {ex.Message}");
        }
    }

    public async Task<ServiceResult> UpdateAsync(NationalityDto dto)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.UpdateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            
            if (result)
                return ServiceResult.Success("Nationality updated successfully");
            else
                return ServiceResult.Failure("Failed to update nationality");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error updating nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult> DeleteAsync(int id)
    {
        try
        {
            var result = await _unitOfWork.Nationalities.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();

            if (result)
                return ServiceResult.Success("Nationality deleted successfully");
            else
                return ServiceResult.Failure("Failed to delete nationality");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"Error deleting nationality: {ex.Message}");
        }
    }

    public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
    {
        try
        {
            var nationalities = await _unitOfWork.Nationalities.GetAllAsync();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الجنسيات");

            // Headers in Arabic
            worksheet.Cells[1, 1].Value = "اسم الجنسية";
            worksheet.Cells[1, 2].Value = "الرمز";
            worksheet.Cells[1, 3].Value = "الوصف";
            worksheet.Cells[1, 4].Value = "نشط";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 4])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // Data rows
            int row = 2;
            foreach (var nationality in nationalities)
            {
                worksheet.Cells[row, 1].Value = nationality.Name;
                worksheet.Cells[row, 2].Value = nationality.Code;
                worksheet.Cells[row, 3].Value = nationality.Description;
                worksheet.Cells[row, 4].Value = nationality.IsActive ? "نعم" : "لا";
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            var excelData = package.GetAsByteArray();
            return ServiceResult<byte[]>.Success(excelData, "تم تصدير الجنسيات بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult<byte[]>.Failure($"خطأ في تصدير الجنسيات: {ex.Message}");
        }
    }

    public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
    {
        try
        {
            int importedCount = 0;
            var errors = new List<string>();

            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
            }

            // Check if we have data
            if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
            {
                return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
            }

            // Process each row (skip header row)
            for (int row = 2; row <= worksheet.Dimension.Rows; row++)
            {
                try
                {
                    var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                    var code = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                    var description = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                    var isActiveText = worksheet.Cells[row, 4].Value?.ToString()?.Trim();

                    // Validate required fields
                    if (string.IsNullOrEmpty(name))
                    {
                        errors.Add($"الصف {row}: اسم الجنسية مطلوب");
                        continue;
                    }

                    // Check if nationality already exists
                    var exists = await _unitOfWork.Nationalities.ExistsAsync(name);
                    if (exists)
                    {
                        errors.Add($"الصف {row}: جنسية بالاسم {name} موجودة بالفعل");
                        continue;
                    }

                    // Parse IsActive
                    bool isActive = true; // default
                    if (!string.IsNullOrEmpty(isActiveText))
                    {
                        isActive = isActiveText.ToLower() == "نعم" || isActiveText.ToLower() == "yes" || isActiveText == "1";
                    }

                    var nationalityDto = new NationalityDto
                    {
                        Name = name,
                        Code = code,
                        Description = description,
                        IsActive = isActive,
                        CreatedAt = DateTime.Now // Set CreatedAt to current time during import
                    };

                    var createResult = await _unitOfWork.Nationalities.CreateAsync(nationalityDto);
                    if (createResult)
                    {
                        importedCount++;
                    }
                    else
                    {
                        errors.Add($"الصف {row}: فشل في إضافة الجنسية {name}");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                }
            }

            await _unitOfWork.SaveChangesAsync();

            var message = $"تم استيراد {importedCount} جنسية بنجاح";
            if (errors.Any())
            {
                message += $". عدد الأخطاء: {errors.Count}";
            }

            var finalResult = ServiceResult<int>.Success(importedCount, message);
            finalResult.Errors = errors;
            return finalResult;
        }
        catch (Exception ex)
        {
            return ServiceResult<int>.Failure($"خطأ في استيراد الجنسيات: {ex.Message}");
        }
    }
}