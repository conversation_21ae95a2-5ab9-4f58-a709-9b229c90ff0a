@{
    ViewData["Title"] = "مدير الموارد البشرية";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">مدير الموارد البشرية</h2>

            <!-- Navigation Cards -->
            <div class="row mb-4">
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">📝 معالجة الطلبات</h5>
                            <p class="card-text">معالجة الطلبات المحولة واتخاذ الإجراءات المناسبة (اعتماد، إلغاء، إعادة)</p>
                            <a href="@Url.Action("ProcessOrder", "HRManager")" class="btn btn-primary">
                                معالجة الطلبات
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">🔄 تغيير حالة الطلبات</h5>
                            <p class="card-text">إدارة حالة الطلبات وتغييرها حسب الحاجة مع إمكانية البحث والتصفية</p>
                            <a href="@Url.Action("ChangeStatus", "HRManager")" class="btn btn-success">
                                تغيير حالة الطلبات
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="alert alert-info text-center">
                <h4 class="alert-heading">مرحباً بك في لوحة تحكم مدير الموارد البشرية</h4>
                <p class="mb-0">اختر المهمة المطلوبة من البطاقات أعلاه للبدء في العمل</p>
            </div>
        </div>
    </div>
</div>
