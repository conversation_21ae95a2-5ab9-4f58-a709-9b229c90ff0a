using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IPathsTableRepository
    {
        Task<List<string>> GetPathSupervisorsAsync(string pathColumn);
        Task<bool> CheckIfExistsInPathAsync(string text, string pathColumn);
        Task AddToPathAsync(string text, string pathColumn);
        Task RemoveFromPathAsync(string text, string pathColumn);
        Task<Dictionary<string, List<string>>> GetAllPathsConfigurationAsync();
        Task<List<PathsTable>> GetAllAsync();
        Task<PathsTable> GetByIdAsync(int id);
        Task AddAsync(PathsTable entity);
        Task UpdateAsync(PathsTable entity);
        Task DeleteAsync(int id);
    }
}
