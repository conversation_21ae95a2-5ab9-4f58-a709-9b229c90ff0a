<script src="~/lib/jquery/dist/jquery.min.js"></script>
<script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
<script src="~/js/Notification.js"></script>
<script src="~/js/site.js" asp-append-version="true"></script>
<script src="~/js/shared-utils.js"></script>

<script src="~/js/dashboard.js" asp-append-version="true"></script>

<script>
    // Show success/error messages from TempData
    @if (TempData["SuccessMessage"] != null)
    {
        <text>SharedUtils.showToast('@TempData["SuccessMessage"]', 'success');</text>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <text>SharedUtils.showToast('@TempData["ErrorMessage"]', 'error');</text>
    }
</script>
