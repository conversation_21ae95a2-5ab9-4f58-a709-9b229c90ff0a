namespace OrderFlowCore.Application.Common
{
    public class OrderServiceOptions
    {
        public const string SectionName = "OrderService";
        
        public int MaxAttachments { get; set; } = 4;
        public int MaxFileSizeInMB { get; set; } = 10;
        public string AllowedFileExtensions { get; set; } = ".pdf,.doc,.docx,.jpg,.jpeg,.png";
        public string UploadDirectory { get; set; } = "uploads";
        public string InitialOrderStatus { get; set; } = "(DM)";
        public int MaxFileNameLength { get; set; } = 100;
    }
} 