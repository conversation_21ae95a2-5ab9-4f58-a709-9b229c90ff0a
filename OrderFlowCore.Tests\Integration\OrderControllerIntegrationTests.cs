using Xunit;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Infrastructure.Data;
using OrderFlowCore.Core.Models;
using System.Net;
using System.Text;
using System.Text.Json;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Infrastructure;
using Microsoft.VisualStudio.TestPlatform.TestHost;

namespace OrderFlowCore.Tests.Integration
{
    public class OrderControllerIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public OrderControllerIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory.WithWebHostBuilder(builder =>
            {
                builder.ConfigureServices(services =>
                {
                    // Remove the existing DbContext registration
                    var descriptor = services.SingleOrDefault(
                        d => d.ServiceType == typeof(DbContextOptions<ApplicationDbContext>));
                    if (descriptor != null)
                        services.Remove(descriptor);

                    // Add in-memory database for testing
                    services.AddDbContext<ApplicationDbContext>(options =>
                    {
                        options.UseInMemoryDatabase("TestDb");
                    });
                });
            });

            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task Get_OrderNew_ReturnsSuccessAndCorrectContentType()
        {
            // Act
            var response = await _client.GetAsync("/Order/New");

            // Assert
            response.EnsureSuccessStatusCode();
            response.Content.Headers.ContentType?.ToString()
                .Should().StartWith("text/html");
        }

        [Fact]
        public async Task Get_OrderDetails_WithValidId_ReturnsSuccess()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            var order = new OrdersTable
            {
                EmployeeName = "Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "**********",
                Nationality = "Saudi",
                MobileNumber = "**********",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                OrderStatus = "Pending",
                CreatedAt = DateTime.UtcNow
            };

            context.OrdersTables.Add(order);
            await context.SaveChangesAsync();

            // Act
            var response = await _client.GetAsync($"/Order/Details/{order.Id}");

            // Assert
            response.EnsureSuccessStatusCode();
        }

        [Fact]
        public async Task Get_OrderDetails_WithInvalidId_ReturnsNotFound()
        {
            // Act
            var response = await _client.GetAsync("/Order/Details/999999");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Get_OrderPrint_ReturnsSuccess()
        {
            // Act
            var response = await _client.GetAsync("/OrderPrint");

            // Assert
            response.EnsureSuccessStatusCode();
            response.Content.Headers.ContentType?.ToString()
                .Should().StartWith("text/html");
        }

        [Fact]
        public async Task Post_OrderPrint_DownloadAttachments_WithValidId_ReturnsFile()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
            
            var order = new OrdersTable
            {
                EmployeeName = "Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "**********",
                Nationality = "Saudi",
                MobileNumber = "**********",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                OrderStatus = "Pending",
                CreatedAt = DateTime.UtcNow,
                File1Url = "/uploads/test1.pdf"
            };

            context.OrdersTables.Add(order);
            await context.SaveChangesAsync();

            var formData = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("orderId", order.Id.ToString())
            });

            // Act
            var response = await _client.PostAsync("/OrderPrint/DownloadAttachments", formData);

            // Assert
            // Note: This might fail if files don't exist, but we're testing the endpoint structure
            response.StatusCode.Should().BeOneOf(HttpStatusCode.OK, HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task Get_Auth_Login_ReturnsSuccess()
        {
            // Act
            var response = await _client.GetAsync("/Auth/Login");

            // Assert
            response.EnsureSuccessStatusCode();
            response.Content.Headers.ContentType?.ToString()
                .Should().StartWith("text/html");
        }

        [Fact]
        public async Task Get_Dashboard_WithoutAuth_RedirectsToLogin()
        {
            // Act
            var response = await _client.GetAsync("/Dashboard");

            // Assert
            response.StatusCode.Should().Be(HttpStatusCode.Redirect);
            response.Headers.Location?.ToString().Should().Contain("/Auth/Login");
        }

        [Fact]
        public async Task Get_Home_Index_ReturnsSuccess()
        {
            // Act
            var response = await _client.GetAsync("/");

            // Assert
            response.EnsureSuccessStatusCode();
            response.Content.Headers.ContentType?.ToString()
                .Should().StartWith("text/html");
        }

        [Theory]
        [InlineData("/Order/New")]
        [InlineData("/OrderPrint")]
        [InlineData("/Auth/Login")]
        [InlineData("/")]
        public async Task Get_Endpoints_ReturnSuccessAndCorrectContentType(string url)
        {
            // Act
            var response = await _client.GetAsync(url);

            // Assert
            response.EnsureSuccessStatusCode();
            response.Content.Headers.ContentType?.ToString()
                .Should().StartWith("text/html");
        }

        [Fact]
        public async Task Database_CanBeSeeded_Successfully()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            // Act
            var user = new User
            {
                Username = "testuser",
                Email = "<EMAIL>",
                Phone = "**********",
                Password = "hashedpassword",
                Role = "User",
                CreatedAt = DateTime.UtcNow
            };

            context.Users.Add(user);
            var result = await context.SaveChangesAsync();

            // Assert
            result.Should().BeGreaterThan(0);
            
            var savedUser = await context.Users.FirstOrDefaultAsync(u => u.Username == "testuser");
            savedUser.Should().NotBeNull();
            savedUser.Email.Should().Be("<EMAIL>");
        }

        [Fact]
        public async Task Database_OrderOperations_WorkCorrectly()
        {
            // Arrange
            using var scope = _factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();

            var order = new OrdersTable
            {
                EmployeeName = "Integration Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "INT123",
                CivilRecord = "**********",
                Nationality = "Saudi",
                MobileNumber = "**********",
                Department = "Testing",
                EmploymentType = "Contract",
                Qualification = "Master",
                OrderType = "Test Request",
                Details = "Integration test order",
                OrderStatus = "New",
                CreatedAt = DateTime.UtcNow
            };

            // Act - Create
            context.OrdersTables.Add(order);
            await context.SaveChangesAsync();

            // Assert - Create
            order.Id.Should().BeGreaterThan(0);

            // Act - Read
            var retrievedOrder = await context.OrdersTables.FindAsync(order.Id);

            // Assert - Read
            retrievedOrder.Should().NotBeNull();
            retrievedOrder.EmployeeName.Should().Be("Integration Test Employee");

            // Act - Update
            retrievedOrder.OrderStatus = "Updated";
            await context.SaveChangesAsync();

            // Assert - Update
            var updatedOrder = await context.OrdersTables.FindAsync(order.Id);
            updatedOrder.OrderStatus.Should().Be("Updated");

            // Act - Delete
            context.OrdersTables.Remove(updatedOrder);
            await context.SaveChangesAsync();

            // Assert - Delete
            var deletedOrder = await context.OrdersTables.FindAsync(order.Id);
            deletedOrder.Should().BeNull();
        }
    }
}
