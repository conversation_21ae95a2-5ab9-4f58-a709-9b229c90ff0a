using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using OrderFlowCore.Web.Extentions;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers;

[AuthorizeRole(UserRole.DirectManager, UserRole.AssistantManager, UserRole.Coordinator, UserRole.Supervisor, UserRole.Manager, UserRole.Admin)]
public class DashboardController : Controller
{
    private readonly IDashboardService _dashboardService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(IDashboardService dashboardService, ILogger<DashboardController> logger)
    {
        _dashboardService = dashboardService;
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {

        _logger.LogInformation("Loading dashboard for user: {Username}", User.Identity?.Name);

        var statisticsResult = await _dashboardService.GetDashboardStatisticsAsync();

        var role = User.GetUserRoleType();
        if(string.IsNullOrWhiteSpace(role))
        {
            role = User.GetUserRoleToDisplay();
        }

        var viewModel = new DashboardViewModel
        {
            Username = User.GetUsername(),
            Role = role,
            Email = User.GetEmail(),
        };

        if (statisticsResult.IsSuccess)
        {
            viewModel.Statistics = statisticsResult.Data ?? new DashboardStatisticsDto();
        }
        else
        {
            viewModel.HasError = true;
            viewModel.ErrorMessage = statisticsResult.Message;
            _logger.LogWarning("Failed to load dashboard statistics: {Error}", statisticsResult.Message);
        }

        return View(viewModel);

    }

}