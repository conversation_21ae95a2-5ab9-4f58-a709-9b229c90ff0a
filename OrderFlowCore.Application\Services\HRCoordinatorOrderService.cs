using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{

    public class HRCoordinatorOrderService : IHRCoordinatorOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISupervisorService _supervisorService;
        private readonly ILogger<HRCoordinatorOrderService> _logger;

        public HRCoordinatorOrderService(
            IUnitOfWork unitOfWork,
            ISupervisorService supervisorService,
            ILogger<HRCoordinatorOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetHRCoordinatorOrdersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetHRCoordinatorPendingOrdersAsync();

                var orderSummaries = orders.Select(order => new OrderSummaryDto
                {
                    Id = order.Id,
                    EmployeeName = order.EmployeeName,
                    Department = order.Department,
                    OrderType = order.OrderType,
                    OrderStatus = order.OrderStatus,
                    CreatedAt = order.CreatedAt
                }).OrderByDescending(o => o.Id).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting HR coordinator orders");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<AutoRoutingInfoDto>> GetAutoRoutingInfoAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<AutoRoutingInfoDto>.Failure("لم يتم العثور على الطلب");
                }

                var nationality = ProcessNationality(order.Nationality);
                var autoRoute = await _unitOfWork.AutoRoutings.GetMatchingRouteAsync(
                    order.OrderType, nationality, order.JobTitle);

                var autoRoutingInfo = new AutoRoutingInfoDto();

                if (autoRoute != null && autoRoute.Status)
                {
                    autoRoutingInfo.IsAvailable = true;
                    autoRoutingInfo.SupervisorsList = autoRoute.Supervisors?.Split(';')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => s.Trim())
                        .ToList() ?? new List<string>();

                    autoRoutingInfo.Message = BuildAutoPathMessage(autoRoutingInfo.SupervisorsList);
                }
                else
                {
                    autoRoutingInfo.IsAvailable = false;
                    autoRoutingInfo.Message = BuildNoAutoPathMessage();
                }

                return ServiceResult<AutoRoutingInfoDto>.Success(autoRoutingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto routing info for order {OrderId}", orderId);
                return ServiceResult<AutoRoutingInfoDto>.Failure("حدث خطأ أثناء جلب معلومات التوجيه التلقائي");
            }
        }

        public async Task<ServiceResult<DirectRoutingInfoDto>> GetDirectRoutingInfoAsync(int orderId)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult<DirectRoutingInfoDto>.Failure("لم يتم العثور على الطلب");
                }

                var nationality = ProcessNationality(order.Nationality);
                var directRoute = await _unitOfWork.DirectRoutings.GetMatchingRouteAsync(
                    order.OrderType, nationality, order.JobTitle);

                var directRoutingInfo = new DirectRoutingInfoDto();

                if (directRoute != null && directRoute.Status)
                {
                    directRoutingInfo.IsAvailable = true;
                    directRoutingInfo.SupervisorsList = directRoute.Supervisors?.Split(';')
                        .Where(s => !string.IsNullOrWhiteSpace(s))
                        .Select(s => s.Trim())
                        .ToList() ?? new List<string>();

                    directRoutingInfo.Message = BuildDirectPathMessage(directRoutingInfo.SupervisorsList);
                }
                else
                {
                    directRoutingInfo.IsAvailable = false;
                    directRoutingInfo.Message = BuildNoDirectPathMessage();
                }

                return ServiceResult<DirectRoutingInfoDto>.Success(directRoutingInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting direct routing info for order {OrderId}", orderId);
                return ServiceResult<DirectRoutingInfoDto>.Failure("حدث خطأ أثناء جلب معلومات المسار السريع");
            }
        }

        public async Task<ServiceResult> SubmitOrderByHrCoordinatorAsync(int orderId, string details, List<string> selectedSupervisors, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                if (selectedSupervisors == null || selectedSupervisors.Count == 0)
                {
                    return ServiceResult.Failure("يجب تحديد مشرف واحد على الأقل");
                }

                var result = await SubmitOrderToSupervisorsAsync(order, details, selectedSupervisors, userName, OrderTransferTypes.Manually);
                if (!result.IsSuccess)
                {
                    return result;
                }

                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تحويل الطلب للاعتماد من المشرفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order {OrderId} to supervisors", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب");
            }
        }

        public async Task<ServiceResult> AutoRouteOrderAsync(int orderId, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                var autoRoutingInfo = await GetAutoRoutingInfoAsync(orderId);
                if (!autoRoutingInfo.IsSuccess || !autoRoutingInfo.Data.IsAvailable)
                {
                    return ServiceResult.Failure("لا يوجد مسار تلقائي متاح لهذا الطلب");
                }

                // Apply auto routing
                var supervisorsList = autoRoutingInfo.Data.SupervisorsList;
                var result = await SubmitOrderToSupervisorsAsync(order, "تم التوجيه التلقائي", supervisorsList, userName, OrderTransferTypes.Auto);

                if (!result.IsSuccess)
                {
                    return result;
                }

                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تطبيق التوجيه التلقائي بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error auto-routing order {OrderId}", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء التوجيه التلقائي");
            }
        }

        public async Task<ServiceResult> RejectOrderByHRCoordinatorAsync(int orderId, string rejectReason, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Order was already assigned, update with rejection
                var assignmentDate = ExtractAssignmentDate(order.ConfirmedByCoordinator);
                order.ConfirmedByCoordinator = OrderHelper.RejectedByWithAssignment(userName, assignmentDate);

                order.OrderStatus = OrderStatus.CancelledByCoordinator;
                order.ReasonForCancellation = rejectReason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إلغاء الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {OrderId} by HR coordinator", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء إلغاء الطلب");
            }
        }

        public async Task<ServiceResult> DirectOrderToManagerAsync(int orderId, string details, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                // Order was already assigned, update with direct to manager
                var assignmentDate = ExtractAssignmentDate(order.ConfirmedByCoordinator);
                order.ConfirmedByCoordinator = OrderHelper.OrderDirectToManagerWithAssignment(userName, assignmentDate);

                order.HumanResourcesManager = OrderHelper.AssignedToHRManager();
                order.OrderStatus = OrderStatus.D;
                order.CoordinatorDetails = details;
                order.TransferType = OrderTransferTypes.DirectToManager;
                order.ReasonForCancellation = null;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تحويل الطلب مباشرة إلى مدير الموارد البشرية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error directing order {OrderId} to manager", orderId);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب للمدير");
            }
        }

        public async Task<ServiceResult> ReturnOrderToAssistantManagerAsync(int orderId, string reason, string? userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(reason))
                {
                    return ServiceResult.Failure("سبب الإعادة مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Order was already assigned, update with return
                var assignmentDate = ExtractAssignmentDate(order.ConfirmedByCoordinator);
                order.ConfirmedByCoordinator = OrderHelper.ReturnedByWithAssignment(userName, assignmentDate);

                order.OrderStatus = OrderStatus.ReturnedByCoordinator;
                order.ReasonForCancellation = reason;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم إعادة الطلب إلى مساعد المدير بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"خطأ في إعادة الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> OrderNeedsActionByCoordinatorAsync(int orderId, string actionDetails, string username)
        {
            if (orderId == 0)
                return ServiceResult.Failure("يرجى اختيار رقم الطلب.");
            if (string.IsNullOrWhiteSpace(actionDetails))
                return ServiceResult.Failure("يرجى إدخال الإجراءات المطلوبة.");

            var order = await _unitOfWork.Orders.GetOrderByIdAsync(orderId);
            if (order == null)
                return ServiceResult.Failure("لم يتم العثور على الطلب.");

            // Order was already assigned, update with needs action
            var assignmentDate = ExtractAssignmentDate(order.ConfirmedByCoordinator);
            order.ConfirmedByCoordinator = OrderHelper.OrderNeedActionByCoordinatorWithAssignment(username, assignmentDate);

            order.OrderStatus = OrderStatus.ActionRequired;
            order.CoordinatorDetails = actionDetails;
            order.ReasonForCancellation = null;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم حفظ طلب الإجراءات بنجاح.");
        }

        public async Task<ServiceResult> SubmitOrderToSupervisorsAsync(OrdersTable order, string details, List<string> selectedSupervisors, string coordinatorName, string orderTransferType)
        {
            try
            {
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }


                // Order was already assigned, update with confirmation
                var assignmentDate = ExtractAssignmentDate(order.ConfirmedByCoordinator);
                order.ConfirmedByCoordinator = OrderHelper.ConfirmedByWithAssignment(coordinatorName, assignmentDate);


                // Update order status and coordinator details
                order.OrderStatus = OrderStatus.C;
                order.CoordinatorDetails = details;
                order.TransferType = orderTransferType;
                order.ReasonForCancellation = null;

                // Update selected supervisors
                if (selectedSupervisors == null || selectedSupervisors.Count == 0)
                {
                    return ServiceResult.Failure("يجب تحديد مشرفين لتحويل الطلب");
                }
                var statusWithDate = OrderHelper.OrderUnderImplementation();

                // Update supervisor statuses using the service
                _supervisorService.UpdateSupervisorStatuses(order, selectedSupervisors, statusWithDate);

                await _unitOfWork.Orders.UpdateAsync(order);

                return ServiceResult.Success("تم تحويل الطلب للاعتماد من المشرفين بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error submitting order {OrderId} to supervisors", order.Id);
                return ServiceResult.Failure("حدث خطأ أثناء تحويل الطلب");
            }
        }

        #region Helper Methods

        private string ProcessNationality(string nationality)
        {
            if (nationality != "سعودي")
            {
                return "غير سعودي";
            }

            return nationality;
        }

        private string BuildAutoPathMessage(List<string> supervisors)
        {
            var message = @"<div class='alert alert-info alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>✨ متوفر مسار تلقائي لهذا الطلب</h5>
                <hr><p class='mb-0'>سيتم توجيه الطلب إلى:</p>
                <ul class='list-unstyled mt-2'>";
            foreach (string supervisor in supervisors)
            {
                message += $"<li><i class='fas fa-check-circle text-success'></i> {supervisor.Trim()}</li>";
            }
            message += @"</ul><hr>
                <small class='text-muted'>يمكنك النقر على زر 'التوجيه التلقائي' لتطبيق هذا المسار</small>
                </div>";
            return message;
        }

        private string BuildNoAutoPathMessage()
        {
            return @"<div class='alert alert-secondary alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>ℹ️ لا يوجد مسار تلقائي</h5>
                <p class='mb-0'>هذا الطلب يحتاج إلى توجيه يدوي</p></div>";
        }

        private string BuildDirectPathMessage(List<string> supervisors)
        {
            var message = @"<div class='alert alert-success alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>🚀 متوفر مسار سريع لهذا الطلب</h5>
                <hr><p class='mb-0'>سيتم توجيه الطلب مباشرة إلى:</p>
                <ul class='list-unstyled mt-2'>";
            foreach (string supervisor in supervisors)
            {
                message += $"<li><i class='fas fa-bolt text-warning'></i> {supervisor.Trim()}</li>";
            }
            message += @"</ul><hr>
                <small class='text-muted'>سيتم تطبيق هذا المسار تلقائياً عند تأكيد الطلب</small>
                </div>";
            return message;
        }

        private string BuildNoDirectPathMessage()
        {
            return @"<div class='alert alert-info alert-dismissible text-right' role='alert'>
                <button type='button' class='btn-close' data-bs-dismiss='alert' aria-label='Close'></button>
                <h5 class='alert-heading'>ℹ️ لا يوجد مسار سريع</h5>
                <p class='mb-0'>سيتم تحويل الطلب إلى منسق الموارد البشرية</p></div>";
        }

        private string ExtractAssignmentDate(string status)
        {
            if (string.IsNullOrEmpty(status))
                return DateTime.Now.ToString("yyyy-MM-dd");

            var parts = status.Split(" | ");
            if (parts.Length >= 3)
            {
                // Format: "assignmentDate | action | outDate"
                return parts[0];
            }
            else if (parts.Length >= 2)
            {
                // Format: "date | action" (old format)
                return parts[0];
            }

            return DateTime.Now.ToString("yyyy-MM-dd");
        }

        #endregion
    }
}
