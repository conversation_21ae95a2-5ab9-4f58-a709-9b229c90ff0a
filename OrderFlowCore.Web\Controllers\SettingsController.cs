using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers;

[AuthorizeRole(UserRole.Admin)]
public class SettingsController : Controller
{
    private readonly ILogger<SettingsController> _logger;
    public SettingsController(ILogger<SettingsController> logger)
    {
        _logger = logger;
    }
    public IActionResult Index()
    {
        return View();
    }
} 