@model OrderFlowCore.Web.ViewModels.ResetPasswordViewModel
@{
    ViewData["Title"] = "إعادة تعيين كلمة المرور";
    Layout = "_AuthLayout";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">إعادة تعيين كلمة المرور</div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
                    }
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success">@TempData["SuccessMessage"]</div>
                    }
                    <form id="resetForm" asp-action="ResetPassword" method="post">
                        <div class="mb-3">
                            <label asp-for="UsernameOrEmail" class="form-label"></label>
                            <input asp-for="UsernameOrEmail" class="form-control" />
                            <span asp-validation-for="UsernameOrEmail" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Otp" class="form-label"></label>
                            <input asp-for="Otp" class="form-control" />
                            <span asp-validation-for="Otp" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="NewPassword" class="form-label"></label>
                            <input asp-for="NewPassword" class="form-control" />
                            <span asp-validation-for="NewPassword" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="ConfirmPassword" class="form-label"></label>
                            <input asp-for="ConfirmPassword" class="form-control" />
                            <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                        </div>
                        <button id="resetBtn" type="submit" class="btn btn-success w-100">
                            <span class="btn-text">تغيير كلمة المرور</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        </button>
                    </form>
                    <div class="mt-3 text-center">
                        <a asp-action="Login">العودة لتسجيل الدخول</a>
                    </div>
                    <div id="resetAlert" class="alert mt-3 d-none" role="alert"></div>
                    <script>
                        (function(){
                            const form = document.getElementById('resetForm');
                            const btn = document.getElementById('resetBtn');
                            const alertBox = document.getElementById('resetAlert');
                            form.addEventListener('submit', async (e) => {
                                e.preventDefault();
                                alertBox.classList.add('d-none');
                                const spinner = btn.querySelector('.spinner-border');
                                const text = btn.querySelector('.btn-text');
                                btn.disabled = true; spinner.classList.remove('d-none'); text.classList.add('d-none');
                                try {
                                    const formData = new FormData(form);
                                    const resp = await fetch(form.action, {
                                        method: 'POST',
                                        headers: { 'X-Requested-With': 'XMLHttpRequest' },
                                        body: formData
                                    });
                                    const data = await resp.json();
                                    alertBox.classList.remove('d-none');
                                    alertBox.classList.toggle('alert-success', data.success);
                                    alertBox.classList.toggle('alert-danger', !data.success);
                                    alertBox.textContent = data.message || (data.success ? 'تم التغيير' : 'حدث خطأ');
                                    if (data.success) {
                                        setTimeout(() => { window.location = '@Url.Action("Login")'; }, 1000);
                                    }
                                } catch(err) {
                                    alertBox.classList.remove('d-none','alert-success');
                                    alertBox.classList.add('alert-danger');
                                    alertBox.textContent = 'حدث خطأ أثناء الإرسال';
                                } finally {
                                    btn.disabled = false; spinner.classList.add('d-none'); text.classList.remove('d-none');
                                }
                            });
                        })();
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

