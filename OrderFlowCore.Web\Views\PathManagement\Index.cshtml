@{
    ViewData["Title"] = "إدارة المسارات";
}
<div class="container-fluid py-4">
    <!-- Page Header with Quick Actions -->
    <nav aria-label="breadcrumb" class="mb-3">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="@Url.Action("Index", "PathManagement")">إدارة المسارات</a></li>
            <li class="breadcrumb-item active" aria-current="page">جميع المسارات</li>
        </ol>
    </nav>
    <!-- Enhanced Navigation Tabs -->
    <div class="nav-tabs-custom">
        <ul class="nav nav-pills justify-content-center" role="tablist">
            <li class="nav-item">
                <a class="nav-link active" data-bs-toggle="tab" href="#manual-tab">
                    <i class="fas fa-hand-pointer me-2"></i>التوجيه اليدوي
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#auto-tab">
                    <i class="fas fa-magic me-2"></i>التوجيه التلقائي
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="tab" href="#direct-tab">
                    <i class="fas fa-bolt me-2"></i>التوجيه السريع
                </a>
            </li>
        </ul>
    </div>
    <!-- Tab Content -->
    <div class="tab-content" id="tabContentArea">
        <div class="text-center py-5"><span class="spinner-border"></span></div>
    </div>
</div>
<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteConfirmModalLabel">تأكيد الحذف</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
      </div>
      <div class="modal-body">
        هل أنت متأكد من حذف هذا المسار؟
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
        <button type="button" class="btn btn-danger" id="confirmDeleteBtn">حذف</button>
      </div>
    </div>
  </div>
</div>
<!-- End Delete Confirmation Modal -->
@section Scripts {
    <!-- Set up URLs for AJAX in JS -->
<script>
        window.saveManualPathsUrl = '@Url.Action("SaveManualPaths")';

        window.saveDirectRouteUrl = '@Url.Action("SaveDirectRoute")';
        window.getDirectRouteUrl = '@Url.Action("GetDirectRoute")/';
        window.deleteAutoRouteUrl = '@Url.Action("DeleteAutoRoute")';

        window.saveAutoRouteUrl = '@Url.Action("SaveAutoRoute")';
        window.getAutoRouteUrl = '@Url.Action("GetAutoRoute")/';
        window.deleteDirectRouteUrl = '@Url.Action("DeleteDirectRoute")';

        window.pathTabs = {
            manual: '@Url.Action("ManualTabPartial")',
            auto: '@Url.Action("AutoTabPartial")',
            direct: '@Url.Action("DirectTabPartial")'
        };
    </script>
    <script src="~/js/pathManagement.js"></script>
}


