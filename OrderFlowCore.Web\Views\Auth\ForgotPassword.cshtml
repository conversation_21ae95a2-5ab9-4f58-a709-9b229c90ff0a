@model OrderFlowCore.Web.ViewModels.ForgotPasswordViewModel
@{
    ViewData["Title"] = "استعادة كلمة المرور";
    Layout = "_AuthLayout";
}

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">استعادة كلمة المرور</div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
                    }
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success">@TempData["SuccessMessage"]</div>
                    }
                    <form id="forgotForm" asp-action="ForgotPassword" method="post">
                        <div class="mb-3">
                            <label asp-for="UsernameOrEmail" class="form-label"></label>
                            <input asp-for="UsernameOrEmail" class="form-control" />
                            <span asp-validation-for="UsernameOrEmail" class="text-danger"></span>
                        </div>
                        <button id="forgotBtn" type="submit" class="btn btn-primary w-100">
                            <span class="btn-text">إرسال رمز التحقق</span>
                            <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                        </button>
                    </form>
                    <div class="mt-3 text-center">
                        <a asp-action="Login">العودة لتسجيل الدخول</a>
                    </div>
                    <div id="forgotAlert" class="alert mt-3 d-none" role="alert"></div>

                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        (function(){
            const form = document.getElementById('forgotForm');
            const btn = document.getElementById('forgotBtn');
            const alertBox = document.getElementById('forgotAlert');
            form.addEventListener('submit', async (e) => {
                e.preventDefault();
                alertBox.classList.add('d-none');
                const spinner = btn.querySelector('.spinner-border');
                const text = btn.querySelector('.btn-text');
                btn.disabled = true; spinner.classList.remove('d-none'); text.classList.add('d-none');
                try {
                    const formData = new FormData(form);
                    const resp = await fetch(form.action, {
                        method: 'POST',
                        headers: { 'X-Requested-With': 'XMLHttpRequest' },
                        body: formData
                    });
                    const data = await resp.json();
                    alertBox.classList.remove('d-none');
                    alertBox.classList.toggle('alert-success', data.success);
                    alertBox.classList.toggle('alert-danger', !data.success);
                    alertBox.textContent = data.message || (data.success ? 'تم الإرسال' : 'حدث خطأ');
                    if (data.success) {
                        // pass usernameOrEmail
                        const usernameOrEmail = formData.get('UsernameOrEmail');
                        // Redirect to reset password page with usernameOrEmail after 1s
                        setTimeout(() => {
                            window.location.href = `@Url.Action("ResetPassword", "Auth")?usernameOrEmail=${encodeURIComponent(usernameOrEmail)}`;
                        }, 1000);
                    }
                } catch(err) {
                    alertBox.classList.remove('d-none','alert-success');
                    alertBox.classList.add('alert-danger');
                    alertBox.textContent = 'حدث خطأ أثناء الإرسال';
                } finally {
                    btn.disabled = false; spinner.classList.add('d-none'); text.classList.remove('d-none');
                }
            });
        })();
    </script>
}
