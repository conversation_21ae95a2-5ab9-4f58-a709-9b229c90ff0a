using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels;

public class ForgotPasswordViewModel
{
    [Required(ErrorMessage = "اسم المستخدم أو البريد مطلوب")]
    [Display(Name = "اسم المستخدم أو البريد الإلكتروني")]
    public string UsernameOrEmail { get; set; } = string.Empty;
}

public class ResetPasswordViewModel
{
    [Required]
    [Display(Name = "اسم المستخدم أو البريد الإلكتروني")]
    public string UsernameOrEmail { get; set; } = string.Empty;

    [Required]
    [Display(Name = "رمز التحقق")]
    public string Otp { get; set; } = string.Empty;

    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "كلمة المرور الجديدة")]
    public string NewPassword { get; set; } = string.Empty;

    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "تأكيد كلمة المرور")]
    [Compare("NewPassword", ErrorMessage = "كلمة المرور وتأكيدها غير متطابقين")]
    public string ConfirmPassword { get; set; } = string.Empty;
}

