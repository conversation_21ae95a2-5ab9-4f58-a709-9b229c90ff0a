using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using System.IO;
using OfficeOpenXml;
using OrderFlowCore.Core.Entities;

public class DepartmentService : IDepartmentService
{
    private readonly IUnitOfWork _unitOfWork;

    public DepartmentService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<ServiceResult<IEnumerable<DepartmentDto>>> GetAllDepartmentsAsync()
    {
        try
        {
            var departments = await _unitOfWork.Departments.GetAllAsync();
            return ServiceResult<IEnumerable<DepartmentDto>>.Success(departments);
        }
        catch (Exception ex)
        {
            return ServiceResult<IEnumerable<DepartmentDto>>.Failure($"Error retrieving departments: {ex.Message}");
        }
    }

    public async Task<ServiceResult<DepartmentDto>> GetDepartmentByIdAsync(int id)
    {
        try
        {
            var department = await _unitOfWork.Departments.GetByIdAsync(id);
            if (department == null)
                return ServiceResult<DepartmentDto>.Failure("Department not found");
                
            return ServiceResult<DepartmentDto>.Success(department);
        }
        catch (Exception ex)
        {
            return ServiceResult<DepartmentDto>.Failure($"Error retrieving department: {ex.Message}");
        }
    }

    public async Task<ServiceResult> CreateDepartmentAsync(DepartmentDto dto)
    {
        try
        {
            var exists = await _unitOfWork.Departments.ExistsAsync(dto.Name);
            if (exists)
            {
                return ServiceResult.Failure("القسم موجود بالفعل");
            }

            var result = await _unitOfWork.Departments.CreateAsync(dto);
            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم إضافة القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء إضافة القسم",
                new List<string> { ex.Message });
        }
    }

    public async Task<ServiceResult> UpdateDepartmentAsync(DepartmentDto dto)
    {
        try
        {
            var exists = await _unitOfWork.Departments.ExistsAsync(dto.Name, dto.Id);
            if (exists)
            {
                return ServiceResult.Failure("القسم موجود بالفعل");
            }

            var result = await _unitOfWork.Departments.UpdateAsync(dto);
            if (result == null)
            {
                return ServiceResult.Failure("القسم غير موجود");
            }

            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم تحديث القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء تحديث القسم",
                new List<string> { ex.Message });
        }
    }

    public async Task<ServiceResult> DeleteDepartmentAsync(int id)
    {
        try
        {
            var result = await _unitOfWork.Departments.DeleteAsync(id);
            if (!result)
            {
                return ServiceResult.Failure("القسم غير موجود");
            }

            await _unitOfWork.SaveChangesAsync();
            return ServiceResult.Success("تم حذف القسم بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure("حدث خطأ أثناء حذف القسم",
                new List<string> { ex.Message });
        }
    }

    public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
    {
        try
        {
            var departments = await _unitOfWork.Departments.GetAllAsync();

            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("الأقسام");

            // Headers in Arabic
            worksheet.Cells[1, 1].Value = "اسم القسم";
            worksheet.Cells[1, 2].Value = "الوصف";
            worksheet.Cells[1, 3].Value = "نشط";
            worksheet.Cells[1, 4].Value = "تاريخ الإنشاء";
            worksheet.Cells[1, 5].Value = "مساعد المدير";

            // Style headers
            using (var range = worksheet.Cells[1, 1, 1, 5])
            {
                range.Style.Font.Bold = true;
                range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
            }

            // Data rows
            int row = 2;
            foreach (var department in departments)
            {
                worksheet.Cells[row, 1].Value = department.Name;
                worksheet.Cells[row, 2].Value = department.Description;
                worksheet.Cells[row, 3].Value = department.IsActive ? "نعم" : "لا";
                worksheet.Cells[row, 4].Value = department.AssistantManagerId.ToDisplayName();
                row++;
            }

            // Auto-fit columns
            worksheet.Cells.AutoFitColumns();

            var excelData = package.GetAsByteArray();
            return ServiceResult<byte[]>.Success(excelData, "تم تصدير الأقسام بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult<byte[]>.Failure($"خطأ في تصدير الأقسام: {ex.Message}");
        }
    }

    public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
    {
        try
        {
            int importedCount = 0;
            var errors = new List<string>();

            using var package = new ExcelPackage(excelStream);
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            if (worksheet == null)
            {
                return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
            }

            // Check if we have data
            if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
            {
                return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
            }

            // Process each row (skip header row)
            for (int row = 2; row <= worksheet.Dimension.Rows; row++)
            {
                try
                {
                    var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();
                    var description = worksheet.Cells[row, 2].Value?.ToString()?.Trim();
                    var isActiveText = worksheet.Cells[row, 3].Value?.ToString()?.Trim();
                    var assistantManagerText = worksheet.Cells[row, 5].Value?.ToString()?.Trim();

                    // Validate required fields
                    if (string.IsNullOrEmpty(name))
                    {
                        errors.Add($"الصف {row}: اسم القسم مطلوب");
                        continue;
                    }

                    // Check if department already exists
                    var exists = await _unitOfWork.Departments.ExistsAsync(name);
                    if (exists)
                    {
                        errors.Add($"الصف {row}: قسم بالاسم {name} موجود بالفعل");
                        continue;
                    }

                    // Parse IsActive
                    bool isActive = true; // default
                    if (!string.IsNullOrEmpty(isActiveText))
                    {
                        isActive = isActiveText.ToLower() == "نعم" || isActiveText.ToLower() == "yes" || isActiveText == "1";
                    }

                    // Parse AssistantManagerType
                    var assistantManagerId = AssistantManagerTypeExtensions.ParseAssistantManagerType(assistantManagerText);

                    var departmentDto = new DepartmentDto
                    {
                        Name = name,
                        Description = description,
                        IsActive = isActive,
                        CreatedAt = DateTime.Now,
                        AssistantManagerId = assistantManagerId
                    };

                    var createResult = await _unitOfWork.Departments.CreateAsync(departmentDto);
                    if (createResult != null)
                    {
                        importedCount++;
                    }
                    else
                    {
                        errors.Add($"الصف {row}: فشل في إضافة القسم {name}");
                    }
                }
                catch (Exception ex)
                {
                    errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                }
            }

            await _unitOfWork.SaveChangesAsync();

            var message = $"تم استيراد {importedCount} قسم بنجاح";
            if (errors.Any())
            {
                message += $". عدد الأخطاء: {errors.Count}";
            }

            var finalResult = ServiceResult<int>.Success(importedCount, message);
            finalResult.Errors = errors;
            return finalResult;
        }
        catch (Exception ex)
        {
            return ServiceResult<int>.Failure($"خطأ في استيراد الأقسام: {ex.Message}");
        }
    }
}