@model IEnumerable<OrderFlowCore.Application.DTOs.EmploymentTypeDto>
@{
    ViewData["Title"] = "إدارة أنواع التوظيف";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="page-title">إدارة أنواع التوظيف</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="@Url.Action("Index", "Dashboard")">لوحة التحكم</a></li>
                            <li class="breadcrumb-item active" aria-current="page">أنواع التوظيف</li>
                        </ol>
                    </nav>
                </div>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createModal">
                    <i class="fas fa-plus me-2"></i>إضافة نوع توظيف جديد
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">قائمة أنواع التوظيف</h5>
                    <div class="btn-group">
                        <form asp-action="ExportToExcel" method="post" style="display: inline;">
                            @Html.AntiForgeryToken()
                            <button type="submit" class="btn btn-outline-primary btn-sm me-2" title="تصدير إلى Excel">
                                <i class="fas fa-file-export me-1"></i>تصدير
                            </button>
                        </form>
                        <label class="btn btn-outline-success btn-sm mb-0" title="استيراد من Excel&#10;&#10;الأعمدة المطلوبة بالترتيب:&#10;1. نوع التوظيف (مطلوب)">
                            <i class="fas fa-file-import me-1"></i>استيراد
                            <input type="file" id="importExcelFile" accept=".xlsx,.xls" hidden />
                        </label>
                    </div>
                </div>
                <div class="card-body">
                    @if (!Model.Any())
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد أنواع توظيف</h5>
                            <p class="text-muted">قم بإضافة نوع توظيف جديد للبدء</p>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createModal">
                                <i class="fas fa-plus me-2"></i>إضافة نوع توظيف جديد
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>الرقم</th>
                                        <th>نوع التوظيف</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var employmentType in Model)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-secondary">@employmentType.Id</span>
                                            </td>
                                            <td>
                                                <strong>@employmentType.Name</strong>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-primary" 
                                                            onclick="editEmploymentType(@employmentType.Id, '@employmentType.Name')"
                                                            title="تعديل">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button" 
                                                            class="btn btn-sm btn-outline-danger" 
                                                            onclick="confirmDelete(@employmentType.Id, '@employmentType.Name')"
                                                            title="حذف">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Modal -->
<div class="modal fade" id="createModal" tabindex="-1" aria-labelledby="createModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createModalLabel">إضافة نوع توظيف جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createForm" method="post" asp-action="Create">
                <div class="modal-body">
                    @Html.AntiForgeryToken()
                    <div class="mb-3">
                        <label for="Name" class="form-label">نوع التوظيف</label>
                        <input type="text" class="form-control" id="Name" name="Name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1" aria-labelledby="editModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editModalLabel">تعديل نوع التوظيف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editForm" method="post">
                <div class="modal-body">
                    @Html.AntiForgeryToken()
                    <input type="hidden" id="editId" name="Id">
                    <div class="mb-3">
                        <label for="editName" class="form-label">نوع التوظيف</label>
                        <input type="text" class="form-control" id="editName" name="Name" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف نوع التوظيف: <strong id="employmentTypeName"></strong>؟</p>
                <p class="text-danger"><small>لا يمكن التراجع عن هذا الإجراء.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    @Html.AntiForgeryToken()
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function editEmploymentType(id, name) {
            document.getElementById('editId').value = id;
            document.getElementById('editName').value = name;
            document.getElementById('editForm').action = '@Url.Action("Edit")';
            new bootstrap.Modal(document.getElementById('editModal')).show();
        }

        function confirmDelete(id, name) {
            document.getElementById('employmentTypeName').textContent = name;
            document.getElementById('deleteForm').action = '@Url.Action("Delete")/' + id;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        // Import functionality
        document.getElementById('importExcelFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (!file) return;

            if (confirm('هل أنت متأكد من استيراد بيانات أنواع التوظيف من هذا الملف؟')) {
                const formData = new FormData();
                formData.append('excelFile', file);
                formData.append('__RequestVerificationToken', document.querySelector('input[name="__RequestVerificationToken"]').value);

                // Show loading
                const importBtn = e.target.parentElement;
                const originalText = importBtn.innerHTML;
                importBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري الاستيراد...';
                importBtn.disabled = true;

                fetch('@Url.Action("ImportFromExcel")', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let message = data.message;
                        if (data.errors && data.errors.length > 0) {
                            message += '\n\nالأخطاء:\n' + data.errors.join('\n');
                        }
                        alert(message);
                        location.reload();
                    } else {
                        alert('خطأ: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('حدث خطأ أثناء استيراد الملف');
                })
                .finally(() => {
                    // Reset button
                    importBtn.innerHTML = originalText;
                    importBtn.disabled = false;
                    e.target.value = '';
                });
            } else {
                e.target.value = '';
            }
        });
    </script>
}