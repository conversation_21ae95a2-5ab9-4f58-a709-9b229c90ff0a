{"ConnectionStrings": {"DefaultConnection": "Data Source=.\\SQLEXPRESS01;Initial Catalog=OrderFlowCore2;Integrated Security=True;Encrypt=False;TrustServerCertificate=True"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "FileService": {"MaxFileSizeInMB": 10, "AllowedFileExtensions": ".pdf,.doc,.docx,.jpg,.jpeg,.png", "UploadDirectory": "uploads", "MaxFileNameLength": 100, "CreateDirectoryIfNotExists": true, "OverwriteExistingFiles": false, "DefaultFilePrefix": "file"}, "OrderService": {"MaxAttachments": 4, "MaxFileSizeInMB": 10, "AllowedFileExtensions": ".pdf,.doc,.docx,.jpg,.jpeg,.png", "UploadDirectory": "uploads", "InitialOrderStatus": "(DM)", "MaxFileNameLength": 100}, "EmailSettings": {"Host": "smtp.gmail.com", "Port": 587, "EnableSsl": true, "FromAddress": "<EMAIL>", "FromName": "OrderFlow", "Username": "<EMAIL>", "Password": "laznocbwwzmhgexg"}}