<ul class="navbar-nav">
    @if (User?.Identity?.IsAuthenticated ?? false)
    {
        <li class="nav-item">
            <a class="nav-link" asp-controller="User" asp-action="Profile">
                <i class="fas fa-user me-1"></i> مرحباً @User.Identity?.Name!
            </a>
        </li>
        
        <li class="nav-item">
            <a class="btn" style="background: var(--primary-color); color: white;" asp-controller="Dashboard" asp-action="Index">
                <i class="fas fa-tachometer-alt me-1"></i> لوحة التحكم
            </a>
        </li>
    }
    else
    {
        <li class="nav-item">
            <a class="btn" style="background: var(--primary-color); color: white;" asp-controller="Auth" asp-action="Login">
                <i class="fas fa-sign-in-alt me-1"></i> تسجيل الدخول
            </a>
        </li>
    }
</ul> 