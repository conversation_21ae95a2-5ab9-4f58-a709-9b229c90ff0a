using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using System.IO;
using OfficeOpenXml;

namespace OrderFlowCore.Application.Services
{
    public class JobTypeService : IJobTypeService
    {
        private readonly IUnitOfWork _unitOfWork;
        public JobTypeService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }
        
        public async Task<ServiceResult<IEnumerable<JobTypeDto>>> GetAllAsync()
        {
            try
            {
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();
                return ServiceResult<IEnumerable<JobTypeDto>>.Success(jobTypes);
            }
            catch (Exception ex)
            {
                return ServiceResult<IEnumerable<JobTypeDto>>.Failure($"Error retrieving job types: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult<JobTypeDto>> GetByIdAsync(int id)
        {
            try
            {
                var jobType = await _unitOfWork.JobTypes.GetByIdAsync(id);
                if (jobType == null)
                    return ServiceResult<JobTypeDto>.Failure("Job type not found");
                    
                return ServiceResult<JobTypeDto>.Success(jobType);
            }
            catch (Exception ex)
            {
                return ServiceResult<JobTypeDto>.Failure($"Error retrieving job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> CreateAsync(JobTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.CreateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Job type created successfully");
                else
                    return ServiceResult.Failure("Failed to create job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error creating job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> UpdateAsync(JobTypeDto dto)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.UpdateAsync(dto);
                await _unitOfWork.SaveChangesAsync();
                
                if (result)
                    return ServiceResult.Success("Job type updated successfully");
                else
                    return ServiceResult.Failure("Failed to update job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error updating job type: {ex.Message}");
            }
        }
        
        public async Task<ServiceResult> DeleteAsync(int id)
        {
            try
            {
                var result = await _unitOfWork.JobTypes.DeleteAsync(id);
                await _unitOfWork.SaveChangesAsync();

                if (result)
                    return ServiceResult.Success("Job type deleted successfully");
                else
                    return ServiceResult.Failure("Failed to delete job type");
            }
            catch (Exception ex)
            {
                return ServiceResult.Failure($"Error deleting job type: {ex.Message}");
            }
        }

        public async Task<bool> ExistsAsync(int id) => await _unitOfWork.JobTypes.ExistsAsync(id);

        public async Task<ServiceResult<byte[]>> ExportToExcelAsync()
        {
            try
            {
                var jobTypes = await _unitOfWork.JobTypes.GetAllAsync();

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("أنواع الوظائف");

                // Headers in Arabic
                worksheet.Cells[1, 1].Value = "نوع الوظيفة";

                // Style headers
                using (var range = worksheet.Cells[1, 1, 1, 1])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
                    range.Style.Border.BorderAround(OfficeOpenXml.Style.ExcelBorderStyle.Thin);
                }

                // Data rows
                int row = 2;
                foreach (var jobType in jobTypes)
                {
                    worksheet.Cells[row, 1].Value = jobType.Name;
                    row++;
                }

                // Auto-fit columns
                worksheet.Cells.AutoFitColumns();

                var excelData = package.GetAsByteArray();
                return ServiceResult<byte[]>.Success(excelData, "تم تصدير أنواع الوظائف بنجاح");
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"خطأ في تصدير أنواع الوظائف: {ex.Message}");
            }
        }

        public async Task<ServiceResult<int>> ImportFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        var name = worksheet.Cells[row, 1].Value?.ToString()?.Trim();

                        // Validate required fields
                        if (string.IsNullOrEmpty(name))
                        {
                            errors.Add($"الصف {row}: اسم نوع الوظيفة مطلوب");
                            continue;
                        }

                        // Check if job type already exists
                        var exists = await _unitOfWork.JobTypes.ExistsAsync(name);
                        if (exists)
                        {
                            errors.Add($"الصف {row}: نوع وظيفة بالاسم {name} موجود بالفعل");
                            continue;
                        }

                        var jobTypeDto = new JobTypeDto
                        {
                            Name = name
                        };

                        var createResult = await _unitOfWork.JobTypes.CreateAsync(jobTypeDto);
                        if (createResult)
                        {
                            importedCount++;
                        }
                        else
                        {
                            errors.Add($"الصف {row}: فشل في إضافة نوع الوظيفة {name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                await _unitOfWork.SaveChangesAsync();

                var message = $"تم استيراد {importedCount} نوع وظيفة بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                var finalResult = ServiceResult<int>.Success(importedCount, message);
                finalResult.Errors = errors;
                return finalResult;
            }
            catch (Exception ex)
            {
                return ServiceResult<int>.Failure($"خطأ في استيراد أنواع الوظائف: {ex.Message}");
            }
        }
    }
}