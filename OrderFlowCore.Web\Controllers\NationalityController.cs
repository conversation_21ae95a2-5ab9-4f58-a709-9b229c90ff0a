using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers;

[AuthorizeRole(UserRole.Admin, UserRole.Manager)]
public class NationalityController : Controller
{
    private readonly INationalityService _nationalityService;
    
    private readonly ILogger<NationalityController> _logger;

    public NationalityController(INationalityService nationalityService, ILogger<NationalityController> logger)
    {
        _nationalityService = nationalityService;
        
        _logger = logger;
    }

    public async Task<IActionResult> Index()
    {
        var result = await _nationalityService.GetAllAsync();
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction("Index", "Home");
        }
        
        return View(result.Data);
    }

    public IActionResult Create()
    {
        return View(new NationalityDto());
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Create(NationalityDto nationality)
    {
        if (!ModelState.IsValid)
        {
            return View(nationality);
        }

        var result = await _nationalityService.CreateAsync(nationality);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }

        return View(nationality);
    }

    public async Task<IActionResult> Edit(int id)
    {
        var result = await _nationalityService.GetByIdAsync(id);
        if (!result.IsSuccess)
        {
            TempData["ErrorMessage"] = result.Message;
            return RedirectToAction(nameof(Index));
        }
        
        return View(result.Data);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Edit(NationalityDto nationality)
    {
        if (ModelState.IsValid)
        {
            var result = await _nationalityService.UpdateAsync(nationality);
            if (result.IsSuccess)
            {
                TempData["SuccessMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
            }
        }
        return View(nationality);
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Delete(int id)
    {
        var result = await _nationalityService.DeleteAsync(id);
        if (result.IsSuccess)
        {
            TempData["SuccessMessage"] = result.Message;
        }
        else
        {
            TempData["ErrorMessage"] = result.Message;
        }
        return RedirectToAction(nameof(Index));
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ExportToExcel()
    {
        try
        {
            var result = await _nationalityService.ExportToExcelAsync();
            if (result.IsSuccess)
            {
                var fileName = $"الجنسيات_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                return File(result.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exporting nationalities to Excel");
            TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير الجنسيات";
            return RedirectToAction(nameof(Index));
        }
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> ImportFromExcel(IFormFile excelFile)
    {
        try
        {
            if (excelFile == null || excelFile.Length == 0)
            {
                return Json(new { success = false, message = "يرجى اختيار ملف Excel" });
            }

            if (!excelFile.FileName.EndsWith(".xlsx") && !excelFile.FileName.EndsWith(".xls"))
            {
                return Json(new { success = false, message = "يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)" });
            }

            using var stream = excelFile.OpenReadStream();
            var result = await _nationalityService.ImportFromExcelAsync(stream);

            if (result.IsSuccess)
            {
                return Json(new {
                    success = true,
                    message = result.Message,
                    importedCount = result.Data,
                    errors = result.Errors
                });
            }
            else
            {
                return Json(new { success = false, message = result.Message });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error importing nationalities from Excel");
            return Json(new { success = false, message = "حدث خطأ أثناء استيراد الجنسيات" });
        }
    }
}