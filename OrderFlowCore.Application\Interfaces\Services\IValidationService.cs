using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IValidationService
{
    /// <summary>
    /// Validates email format and returns validation result
    /// </summary>
    ServiceResult ValidateEmail(string email);

    /// <summary>
    /// Validates password strength and returns validation result
    /// </summary>
    ServiceResult ValidatePassword(string password, int minLength = 6);

    /// <summary>
    /// Validates phone number format and returns validation result
    /// </summary>
    ServiceResult ValidatePhone(string? phone);

    /// <summary>
    /// Validates username format and returns validation result
    /// </summary>
    ServiceResult ValidateUsername(string username);

    /// <summary>
    /// Checks if the user is protected from deletion (e.g., Super Admin)
    /// </summary>
    bool IsProtectedUser(string username);

    /// <summary>
    /// Validates password confirmation match
    /// </summary>
    ServiceResult ValidatePasswordConfirmation(string password, string confirmPassword);
}
