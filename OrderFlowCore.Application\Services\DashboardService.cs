using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Application.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DashboardService> _logger;

        public DashboardService(
            IUnitOfWork unitOfWork,
            ILogger<DashboardService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ServiceResult<DashboardStatisticsDto>> GetDashboardStatisticsAsync()
        {
            try
            {
                _logger.LogInformation("Starting dashboard statistics collection");

                var statistics = new DashboardStatisticsDto();

                // Collect all statistics sequentially to avoid DbContext concurrency issues
                statistics.OrderStatistics = await GetOrderStatisticsAsync();
                statistics.DepartmentStatistics = await GetDepartmentStatisticsAsync();
                statistics.EmployeeStatistics = await GetEmployeeStatisticsAsync();
                statistics.WorkflowStatistics = await GetWorkflowStatisticsAsync();

                _logger.LogInformation("Dashboard statistics collection completed successfully");
                return ServiceResult<DashboardStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting dashboard statistics");
                return ServiceResult<DashboardStatisticsDto>.Failure("حدث خطأ أثناء جمع إحصائيات لوحة التحكم");
            }
        }

        public async Task<ServiceResult<HRCoordinatorStatisticsDto>> GetHRCoordinatorStatisticsAsync()
        {
            try
            {
                _logger.LogInformation("Starting HR Coordinator statistics collection");

                var statistics = new HRCoordinatorStatisticsDto();

                // Collect coordinator-specific statistics
                statistics = await GetCoordinatorSpecificStatisticsAsync();

                _logger.LogInformation("HR Coordinator statistics collection completed successfully");
                return ServiceResult<HRCoordinatorStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error collecting HR Coordinator statistics");
                return ServiceResult<HRCoordinatorStatisticsDto>.Failure("حدث خطأ أثناء جمع إحصائيات منسق الموارد البشرية");
            }
        }

        private async Task<OrderStatisticsDto> GetOrderStatisticsAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();
            var orderTypes = await _unitOfWork.OrdersTypes.GetAllAsync();

            var now = DateTime.Now;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(now.Year, now.Month, 1);

            var cancelledStatuses = new[] { OrderStatus.CancelledByDepartmentManager, OrderStatus.CancelledByAssistantManager, OrderStatus.CancelledBySupervisor, OrderStatus.CancelledByCoordinator, OrderStatus.CancelledByManager };

            var totalOrders = orders.Count;
            var cancelledOrders = orders.Count(o => cancelledStatuses.Contains(o.OrderStatus));
            var completedOrders = orders.Count(o => o.OrderStatus == OrderStatus.Accepted);
            var pendingOrders = totalOrders - cancelledOrders - completedOrders; 

            return new OrderStatisticsDto
            {
                TotalOrders = totalOrders,
                PendingOrders = pendingOrders,
                CompletedOrders = completedOrders,
                CancelledOrders = cancelledOrders,
                TodayOrders = orders.Count(o => o.CreatedAt.Date == today),
                ThisWeekOrders = orders.Count(o => o.CreatedAt.Date >= weekStart),
                ThisMonthOrders = orders.Count(o => o.CreatedAt.Date >= monthStart),
                OrdersByStatus = orders.GroupBy(o => o.OrderStatus)
                    .Select(g => new OrderStatusCountDto
                    {
                        Status = g.Key,
                        StatusDisplayName = g.Key.ToDisplayString(),
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                OrdersByType = orders.GroupBy(o => o.OrderType)
                    .Select(g => new OrderTypeCountDto
                    {
                        OrderType = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                OrdersByDepartment = orders.GroupBy(o => o.Department)
                    .Select(g => new DepartmentOrderCountDto
                    {
                        DepartmentName = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalOrders > 0 ? (double)g.Count() / totalOrders * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList()
            };
        }

        private async Task<DepartmentStatisticsDto> GetDepartmentStatisticsAsync()
        {
            var departments = await _unitOfWork.Departments.GetAllAsync();
            var totalDepartments = departments.Count();

            return new DepartmentStatisticsDto
            {
                TotalDepartments = totalDepartments,
                AssignedDepartments = departments.Count(d => d.AssistantManagerId != AssistantManagerType.Unknown),
                UnassignedDepartments = departments.Count(d => d.AssistantManagerId == AssistantManagerType.Unknown),
                DepartmentsByManager = departments
                    .Where(d => d.AssistantManagerId != AssistantManagerType.Unknown)
                    .GroupBy(d => d.AssistantManagerId)
                    .Select(g => new ManagerDepartmentCountDto
                    {
                        ManagerType = g.Key,
                        ManagerName = g.Key.ToDisplayName(),
                        DepartmentCount = g.Count(),
                        Percentage = totalDepartments > 0 ? (double)g.Count() / totalDepartments * 100 : 0
                    }).OrderByDescending(x => x.DepartmentCount).ToList()
            };
        }

        private async Task<EmployeeStatisticsDto> GetEmployeeStatisticsAsync()
        {
            var employees = await _unitOfWork.Employees.GetAllAsync();
            var totalEmployees = employees.Count();

            return new EmployeeStatisticsDto
            {
                TotalEmployees = totalEmployees,
                EmployeesByNationality = employees.GroupBy(e => e.Nationality)
                    .Select(g => new NationalityCountDto
                    {
                        Nationality = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList(),
                EmployeesByType = employees.GroupBy(e => e.EmploymentType)
                    .Select(g => new EmploymentTypeCountDto
                    {
                        EmploymentType = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).ToList(),
                EmployeesByQualification = employees.GroupBy(e => e.Qualification)
                    .Select(g => new QualificationCountDto
                    {
                        Qualification = g.Key ?? "غير محدد",
                        Count = g.Count(),
                        Percentage = totalEmployees > 0 ? (double)g.Count() / totalEmployees * 100 : 0
                    }).OrderByDescending(x => x.Count).Take(10).ToList()
            };
        }



        private async Task<WorkflowStatisticsDto> GetWorkflowStatisticsAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();

            var workflowStats = new WorkflowStatisticsDto
            {
                OrdersAtDirectManager = orders.Count(o => o.OrderStatus == OrderStatus.DM || o.OrderStatus == OrderStatus.ReturnedByAssistantManager),
                OrdersAtAssistantManagers = orders.Count(o => o.OrderStatus == OrderStatus.A1 || o.OrderStatus == OrderStatus.A2 || o.OrderStatus == OrderStatus.A3 || o.OrderStatus == OrderStatus.A4 || o.OrderStatus == OrderStatus.ReturnedByCoordinator),
                OrdersAtCoordinators = orders.Count(o => o.OrderStatus == OrderStatus.B || o.OrderStatus == OrderStatus.ReturnedBySupervisor),
                OrdersAtSupervisors = orders.Count(o => o.OrderStatus == OrderStatus.C || o.OrderStatus == OrderStatus.ReturnedByManager),
                OrdersAtManagers = orders.Count(o => o.OrderStatus == OrderStatus.D),
                OrdersRequiringAction = orders.Count(o => o.OrderStatus == OrderStatus.ActionRequired || o.OrderStatus == OrderStatus.ActionRequiredBySupervisor)
            };

            workflowStats.OrdersByWorkflowStage = new List<WorkflowStageCountDto>
            {
                new() { StageName = "مدير القسم", Count = workflowStats.OrdersAtDirectManager, StageColor = "#007bff" },
                new() { StageName = "مساعد المدير", Count = workflowStats.OrdersAtAssistantManagers, StageColor = "#28a745" },
                new() { StageName = "المنسق", Count = workflowStats.OrdersAtCoordinators, StageColor = "#ffc107" },
                new() { StageName = "المشرف", Count = workflowStats.OrdersAtSupervisors, StageColor = "#17a2b8" },
                new() { StageName = "مدير الموارد البشرية", Count = workflowStats.OrdersAtManagers, StageColor = "#6f42c1" },
                new() { StageName = "يتطلب إجراء", Count = workflowStats.OrdersRequiringAction, StageColor = "#dc3545" }
            };

            var totalWorkflowOrders = workflowStats.OrdersByWorkflowStage.Sum(s => s.Count);
            foreach (var stage in workflowStats.OrdersByWorkflowStage)
            {
                stage.Percentage = totalWorkflowOrders > 0 ? (double)stage.Count / totalWorkflowOrders * 100 : 0;
            }

            return workflowStats;
        }

        private async Task<HRCoordinatorStatisticsDto> GetCoordinatorSpecificStatisticsAsync()
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();
            var now = DateTime.Now;
            var today = now.Date;
            var weekStart = today.AddDays(-(int)today.DayOfWeek);
            var monthStart = new DateTime(now.Year, now.Month, 1);

            // Coordinator-specific order counts
            var coordinatorOrders = orders.Where(o => 
                o.OrderStatus == OrderStatus.B || 
                o.OrderStatus == OrderStatus.ReturnedBySupervisor ||
                o.OrderStatus == OrderStatus.ActionRequired ||
                o.OrderStatus == OrderStatus.CancelledByCoordinator).ToList();

            var statistics = new HRCoordinatorStatisticsDto
            {
                TotalOrdersForCoordinator = coordinatorOrders.Count,
                PendingOrdersForCoordinator = coordinatorOrders.Count(o => o.OrderStatus == OrderStatus.B),
                ProcessedOrdersToday = coordinatorOrders.Count(o => o.CreatedAt.Date == today),
                ProcessedOrdersThisWeek = coordinatorOrders.Count(o => o.CreatedAt.Date >= weekStart),
                ProcessedOrdersThisMonth = coordinatorOrders.Count(o => o.CreatedAt.Date >= monthStart),
                OrdersRequiringAction = orders.Count(o => o.OrderStatus == OrderStatus.ActionRequired),
                OrdersAtSupervisors = orders.Count(o => o.OrderStatus == OrderStatus.C),
                
            };

            // Top departments
            statistics.TopDepartments = coordinatorOrders.GroupBy(o => o.Department)
                .Select(g => new DepartmentOrderCountDto
                {
                    DepartmentName = g.Key ?? "غير محدد",
                    Count = g.Count(),
                    Percentage = coordinatorOrders.Count > 0 ? (double)g.Count() / coordinatorOrders.Count * 100 : 0,
                    PendingCount = g.Count(o => o.OrderStatus == OrderStatus.B),
                    CompletedCount = g.Count(o => o.OrderStatus == OrderStatus.C)
                }).OrderByDescending(x => x.Count).Take(10).ToList();


            // Recent activities
            statistics.RecentActivities = coordinatorOrders
                .OrderByDescending(o => o.CreatedAt)
                .Take(10)
                .Select(o => new RecentActivityDto
                {
                    OrderId = o.Id,
                    EmployeeName = o.EmployeeName ?? "غير محدد",
                    Department = o.Department ?? "غير محدد",
                    Action = GetActionFromStatus(o.OrderStatus),
                    ActionDescription = GetActionDescription(o.OrderStatus),
                    Timestamp = o.CreatedAt,
                    Status = o.OrderStatus.ToDisplayString(),
                    Icon = GetActionIcon(o.OrderStatus),
                    Color = GetStatusColor(o.OrderStatus)
                }).ToList();

            return statistics;
        }

        private string GetStatusColor(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.B => "#ffc107", // Yellow for pending
                OrderStatus.C => "#17a2b8", // Blue for at supervisors
                OrderStatus.ReturnedBySupervisor => "#fd7e14", // Orange for returned
                OrderStatus.ActionRequired => "#dc3545", // Red for action required
                OrderStatus.CancelledByCoordinator => "#6c757d", // Gray for cancelled
                _ => "#6c757d"
            };
        }

        private string GetActionFromStatus(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.B => "طلب جديد",
                OrderStatus.C => "تم التوجيه",
                OrderStatus.ReturnedBySupervisor => "مُعاد من المشرف",
                OrderStatus.ActionRequired => "يتطلب إجراء",
                OrderStatus.CancelledByCoordinator => "تم الإلغاء",
                _ => "تحديث"
            };
        }

        private string GetActionDescription(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.B => "طلب جديد يحتاج معالجة",
                OrderStatus.C => "تم توجيه الطلب للمشرفين",
                OrderStatus.ReturnedBySupervisor => "تم إعادة الطلب من المشرف",
                OrderStatus.ActionRequired => "الطلب يحتاج إجراءات إضافية",
                OrderStatus.CancelledByCoordinator => "تم إلغاء الطلب من قبل المنسق",
                _ => "تم تحديث حالة الطلب"
            };
        }

        private string GetActionIcon(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.B => "fas fa-clock",
                OrderStatus.C => "fas fa-paper-plane",
                OrderStatus.ReturnedBySupervisor => "fas fa-undo",
                OrderStatus.ActionRequired => "fas fa-exclamation-triangle",
                OrderStatus.CancelledByCoordinator => "fas fa-times-circle",
                _ => "fas fa-edit"
            };
        }
    }
}
