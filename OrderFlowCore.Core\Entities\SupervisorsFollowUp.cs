﻿
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;


namespace OrderFlowCore.Core.Models;

public partial class SupervisorsFollowUp
{
    [MaxLength(100)]
    public string SupervisorId { get; set; }

    [StringLength(50)]
    public string CivilRecord { get; set; }

    [Required]
    [StringLength(100)]
    public string OwnerName { get; set; }

    [Required]
    [StringLength(200)]
    public string SpecialProcedure { get; set; }
}