using OrderFlowCore.Application.Interfaces.Services;
using System.Collections.Generic;

namespace OrderFlowCore.Application.DTOs
{
    public class DistributionDto
    {
        public IEnumerable<DepartmentDistributionDto> DepartmentDistribution { get; set; } = new List<DepartmentDistributionDto>();
        public IEnumerable<DistributionStatsDto> DistributionStats { get; set; } = new List<DistributionStatsDto>();
        public IEnumerable<string> AvailableDepartments { get; set; } = new List<string>();
    }
} 