using Xunit;
using Moq;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Services;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Core.Exceptions;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Tests.Services
{
    public class OrderServiceTests
    {
        private readonly Mock<IUnitOfWork> _mockUnitOfWork;
        private readonly Mock<IEnvironmentService> _mockEnvironmentService;
        private readonly Mock<IFileService> _mockFileService;
        private readonly Mock<ILogger<OrderService>> _mockLogger;
        private readonly Mock<IOptions<OrderServiceOptions>> _mockOptions;
        private readonly OrderService _orderService;

        public OrderServiceTests()
        {
            _mockUnitOfWork = new Mock<IUnitOfWork>();
            _mockEnvironmentService = new Mock<IEnvironmentService>();
            _mockFileService = new Mock<IFileService>();
            _mockLogger = new Mock<ILogger<OrderService>>();
            _mockOptions = new Mock<IOptions<OrderServiceOptions>>();

            var options = new OrderServiceOptions
            {
                MaxAttachments = 4,
                MaxFileSizeInMB = 10,
                AllowedFileExtensions = ".pdf,.doc,.docx,.jpg,.jpeg,.png",
                UploadDirectory = "uploads",
                InitialOrderStatus = OrderStatus.DM.ToString(),
                MaxFileNameLength = 100
            };

            _mockOptions.Setup(x => x.Value).Returns(options);

            _orderService = new OrderService(
                _mockUnitOfWork.Object,
                _mockEnvironmentService.Object,
                _mockOptions.Object,
                _mockFileService.Object,
                _mockLogger.Object
            );
        }

        [Fact]
        public async Task GetOrderDetailsAsync_WithValidId_ReturnsOrderDetails()
        {
            // Arrange
            var orderId = 1;
            var order = new OrdersTable
            {
                Id = orderId,
                EmployeeName = "Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "1234567890",
                Nationality = "Saudi",
                MobileNumber = "0501234567",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                OrderStatus = OrderStatus.DM,
                CreatedAt = DateTime.UtcNow
            };

            var mockOrderRepository = new Mock<IOrderRepository>();
            mockOrderRepository.Setup(x => x.GetByIdAsync(orderId))
                .ReturnsAsync(order);

            _mockUnitOfWork.Setup(x => x.Orders)
                .Returns(mockOrderRepository.Object);

            // Act
            var result = await _orderService.GetOrderDetailsAsync(orderId);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().NotBeNull();
            result.Data.Id.Should().Be(orderId);
            result.Data.EmployeeName.Should().Be("Test Employee");
        }

        [Fact]
        public async Task GetOrderDetailsAsync_WithInvalidId_ThrowsValidationException()
        {
            // Arrange
            var invalidOrderId = -1;

            // Act & Assert
            await Assert.ThrowsAsync<ValidationException>(() => 
                _orderService.GetOrderDetailsAsync(invalidOrderId));
        }

        [Fact]
        public async Task GetOrderDetailsAsync_WithNonExistentId_ThrowsOrderNotFoundException()
        {
            // Arrange
            var orderId = 999;
            var mockOrderRepository = new Mock<IOrderRepository>();
            mockOrderRepository.Setup(x => x.GetByIdAsync(orderId))
                .ReturnsAsync((OrdersTable)null);

            _mockUnitOfWork.Setup(x => x.Orders)
                .Returns(mockOrderRepository.Object);

            // Act & Assert
            await Assert.ThrowsAsync<OrderNotFoundException>(() => 
                _orderService.GetOrderDetailsAsync(orderId));
        }

        [Fact]
        public async Task CreateOrderAsync_WithValidData_ReturnsSuccess()
        {
            // Arrange
            var orderDto = new OrderNewDto
            {
                EmployeeName = "Test Employee",
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "1234567890",
                Nationality = "Saudi",
                MobileNumber = "0501234567",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                Attachments = new List<byte[]>()
            };

            var mockOrderRepository = new Mock<IOrderRepository>();
            mockOrderRepository.Setup(x => x.AddAsync(It.IsAny<OrdersTable>()))
                .ReturnsAsync(1);

            _mockUnitOfWork.Setup(x => x.Orders)
                .Returns(mockOrderRepository.Object);
            _mockUnitOfWork.Setup(x => x.SaveChangesAsync())
                .ReturnsAsync(1);

            _mockFileService.Setup(x => x.UploadFilesAsync(It.IsAny<List<byte[]>>(), It.IsAny<string>()))
                .ReturnsAsync(ServiceResult<List<string>>.Success(new List<string>()));

            // Act
            var result = await _orderService.CreateOrderAsync(orderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.EmployeeName.Should().Be("Test Employee");
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public async Task CreateOrderAsync_WithInvalidEmployeeName_ReturnsFailure(string employeeName)
        {
            // Arrange
            var orderDto = new OrderNewDto
            {
                EmployeeName = employeeName,
                JobTitle = "Test Job",
                EmployeeNumber = "12345",
                CivilRecord = "1234567890",
                Nationality = "Saudi",
                MobileNumber = "0501234567",
                Department = "IT",
                EmploymentType = "Full Time",
                Qualification = "Bachelor",
                OrderType = "Leave Request",
                Details = "Test details",
                Attachments = new List<byte[]>()
            };

            // Act
            var result = await _orderService.CreateOrderAsync(orderDto);

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeFalse();
            result.Message.Should().Contain("اسم الموظف");
        }

        [Fact]
        public async Task GetPrintableOrdersAsync_WithValidFilter_ReturnsOrders()
        {
            // Arrange
            var orders = new List<OrdersTable>
            {
                new OrdersTable
                {
                    Id = 1,
                    EmployeeName = "Employee 1",
                    CreatedAt = DateTime.Today
                },
                new OrdersTable
                {
                    Id = 2,
                    EmployeeName = "Employee 2",
                    CreatedAt = DateTime.Today.AddDays(-1)
                }
            };

            var mockOrderRepository = new Mock<IOrderRepository>();
            mockOrderRepository.Setup(x => x.GetAllAsync())
                .ReturnsAsync(orders);

            _mockUnitOfWork.Setup(x => x.Orders)
                .Returns(mockOrderRepository.Object);

            // Act
            var result = await _orderService.GetPrintableOrdersAsync("", "today");

            // Assert
            result.Should().NotBeNull();
            result.IsSuccess.Should().BeTrue();
            result.Data.Should().HaveCount(1);
            result.Data.First().EmployeeName.Should().Be("Employee 1");
        }
    }
}
