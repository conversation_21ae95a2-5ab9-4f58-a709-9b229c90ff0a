﻿
#nullable disable
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OrderFlowCore.Core.Models;

public partial class Employee
{
    public int Id { get; set; }

    [Required]
    [StringLength(100)]
    public string Name { get; set; }

    [StringLength(100)]
    public string Job { get; set; }

    [StringLength(50)]
    public string EmployeeNumber { get; set; }

    [StringLength(50)]
    public string CivilNumber { get; set; }

    [StringLength(50)]
    public string Nationality { get; set; }

    [StringLength(15)]
    public string Mobile { get; set; }

    [StringLength(50)]
    public string EmploymentType { get; set; }

    [StringLength(50)]
    public string Qualification { get; set; }
}